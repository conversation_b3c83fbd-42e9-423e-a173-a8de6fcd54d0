#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的Excel数据库设计文档分析工具
每个Excel文件包含多个工作表，每个工作表代表一个数据库表
"""

import pandas as pd
import json
import re
from pathlib import Path

class ComprehensiveExcelAnalyzer:
    def __init__(self):
        self.system_modules = {
            'banyan': '业务管理系统',
            'cas': 'CAS认证系统', 
            'cello': '财务支付系统',
            'crm': 'CRM客户关系管理系统',
            'egg': 'EGG系统',
            'horn': 'HORN系统',
            'lqscrm': '联劝CRM系统',
            'statistics': '统计分析系统',
            'taxus': 'TAXUS系统',
            'xxbz': '小小包子系统'
        }
    
    def parse_single_table(self, file_path, sheet_name):
        """解析单个工作表（数据库表）"""
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
            
            table_info = {
                'sheet_name': sheet_name,
                'table_name': sheet_name,  # 工作表名就是表名
                'description': '',
                'fields': [],
                'field_count': 0,
                'has_structure': False
            }
            
            # 查找字段定义的开始位置
            field_start_row = None
            column_mapping = {}
            
            # 扫描前20行寻找字段定义标题
            for i in range(min(20, len(df))):
                row = df.iloc[i]
                
                # 检查是否包含字段定义的标题
                has_field_headers = False
                for j, cell in enumerate(row):
                    if pd.notna(cell):
                        cell_str = str(cell).strip().lower()
                        if any(keyword in cell_str for keyword in ['关键字', '字段名', 'field', 'column']):
                            has_field_headers = True
                            column_mapping['field_name'] = j
                        elif any(keyword in cell_str for keyword in ['数据类型', 'type', '类型']):
                            column_mapping['data_type'] = j
                        elif any(keyword in cell_str for keyword in ['长度', 'length', 'size']):
                            column_mapping['length'] = j
                        elif any(keyword in cell_str for keyword in ['说明', '注释', 'comment', '备注']):
                            column_mapping['comment'] = j
                        elif any(keyword in cell_str for keyword in ['允许空', 'null', '空值']):
                            column_mapping['nullable'] = j
                        elif any(keyword in cell_str for keyword in ['默认值', 'default']):
                            column_mapping['default'] = j
                
                if has_field_headers:
                    field_start_row = i
                    table_info['has_structure'] = True
                    break
            
            # 如果找到了字段定义，开始解析字段
            if field_start_row is not None and 'field_name' in column_mapping:
                field_name_col = column_mapping['field_name']
                
                for i in range(field_start_row + 1, len(df)):
                    row = df.iloc[i]
                    
                    # 获取字段名
                    if field_name_col < len(row):
                        field_name = row.iloc[field_name_col]
                        
                        if pd.notna(field_name) and str(field_name).strip():
                            field_name_str = str(field_name).strip()
                            
                            # 跳过明显不是字段名的行
                            if field_name_str.lower() in ['no', '序号', '关键字', 'field']:
                                continue
                            
                            field_info = {
                                'name': field_name_str,
                                'type': '',
                                'length': '',
                                'comment': '',
                                'nullable': '',
                                'default': ''
                            }
                            
                            # 获取其他字段属性
                            for attr, col_idx in column_mapping.items():
                                if attr != 'field_name' and col_idx < len(row):
                                    cell_value = row.iloc[col_idx]
                                    if pd.notna(cell_value):
                                        field_info[attr.replace('data_type', 'type')] = str(cell_value).strip()
                            
                            table_info['fields'].append(field_info)
                        else:
                            # 如果连续遇到空字段名，可能已经到了表结构的末尾
                            break
            
            table_info['field_count'] = len(table_info['fields'])
            return table_info
            
        except Exception as e:
            return {
                'sheet_name': sheet_name,
                'table_name': sheet_name,
                'error': str(e),
                'has_structure': False,
                'field_count': 0
            }
    
    def analyze_excel_file(self, file_path):
        """分析单个Excel文件的所有工作表"""
        file_name = Path(file_path).stem
        system_key = file_name.replace('_dataBaseModelDesign', '')
        system_name = self.system_modules.get(system_key, system_key)
        
        print(f"\n正在分析系统: {system_name} ({file_name})")
        print("=" * 80)
        
        try:
            excel_obj = pd.ExcelFile(file_path)
            sheet_names = excel_obj.sheet_names
            
            system_info = {
                'system_key': system_key,
                'system_name': system_name,
                'file_name': file_name,
                'total_sheets': len(sheet_names),
                'tables': {},
                'summary': {
                    'total_tables': 0,
                    'tables_with_structure': 0,
                    'total_fields': 0,
                    'main_tables': []
                }
            }
            
            print(f"发现 {len(sheet_names)} 个工作表（数据库表）")
            
            # 分析每个工作表
            for i, sheet_name in enumerate(sheet_names, 1):
                print(f"  [{i:3d}/{len(sheet_names)}] 分析表: {sheet_name}")
                
                table_info = self.parse_single_table(file_path, sheet_name)
                system_info['tables'][sheet_name] = table_info
                
                # 更新统计信息
                if 'error' not in table_info:
                    system_info['summary']['total_tables'] += 1
                    if table_info['has_structure']:
                        system_info['summary']['tables_with_structure'] += 1
                        system_info['summary']['total_fields'] += table_info['field_count']
                        
                        if table_info['field_count'] > 5:  # 认为字段数>5的是主要表
                            system_info['summary']['main_tables'].append({
                                'name': sheet_name,
                                'field_count': table_info['field_count']
                            })
                
                # 显示进度
                if table_info.get('has_structure', False):
                    print(f"      -> {table_info['field_count']} 个字段")
                elif 'error' in table_info:
                    print(f"      -> 解析错误")
                else:
                    print(f"      -> 无标准表结构")
            
            # 排序主要表
            system_info['summary']['main_tables'].sort(key=lambda x: x['field_count'], reverse=True)
            
            return system_info
            
        except Exception as e:
            print(f"错误: 无法分析文件 {file_path}: {e}")
            return {
                'system_key': system_key,
                'system_name': system_name,
                'file_name': file_name,
                'error': str(e)
            }
    
    def analyze_all_systems(self):
        """分析所有系统"""
        current_dir = Path('.')
        excel_files = list(current_dir.glob('*.xls*'))
        
        print(f"发现 {len(excel_files)} 个Excel数据库设计文件")
        
        all_systems = {}
        overall_stats = {
            'total_systems': len(excel_files),
            'total_sheets': 0,
            'total_tables_with_structure': 0,
            'total_fields': 0
        }
        
        for excel_file in excel_files:
            system_info = self.analyze_excel_file(excel_file)
            all_systems[system_info['system_key']] = system_info
            
            # 更新总体统计
            if 'error' not in system_info:
                overall_stats['total_sheets'] += system_info['total_sheets']
                overall_stats['total_tables_with_structure'] += system_info['summary']['tables_with_structure']
                overall_stats['total_fields'] += system_info['summary']['total_fields']
        
        # 保存详细分析结果
        with open('comprehensive_database_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(all_systems, f, ensure_ascii=False, indent=2)
        
        # 生成总体报告
        self.generate_comprehensive_report(all_systems, overall_stats)
        
        return all_systems
    
    def generate_comprehensive_report(self, all_systems, overall_stats):
        """生成全面的分析报告"""
        print("\n" + "=" * 100)
        print("上海联劝公益基金会数据库结构全面分析报告")
        print("=" * 100)
        
        print(f"\n📊 总体统计:")
        print(f"  • 系统模块数量: {overall_stats['total_systems']}")
        print(f"  • 工作表总数: {overall_stats['total_sheets']}")
        print(f"  • 有结构的表数量: {overall_stats['total_tables_with_structure']}")
        print(f"  • 字段总数: {overall_stats['total_fields']}")
        
        print(f"\n📋 各系统详细信息:")
        
        for system_key, system_info in all_systems.items():
            if 'error' in system_info:
                print(f"\n❌ {system_info['system_name']}: 分析失败")
                print(f"   错误: {system_info['error']}")
                continue
            
            summary = system_info['summary']
            print(f"\n✅ {system_info['system_name']}:")
            print(f"   • 工作表数量: {system_info['total_sheets']}")
            print(f"   • 有结构的表: {summary['tables_with_structure']}")
            print(f"   • 字段总数: {summary['total_fields']}")
            
            if summary['main_tables']:
                print(f"   • 主要表 (字段数>5):")
                for table in summary['main_tables'][:5]:  # 显示前5个主要表
                    print(f"     - {table['name']}: {table['field_count']}字段")
                if len(summary['main_tables']) > 5:
                    print(f"     ... 还有 {len(summary['main_tables']) - 5} 个主要表")

if __name__ == "__main__":
    analyzer = ComprehensiveExcelAnalyzer()
    analyzer.analyze_all_systems()
