# 上海联劝公益基金会数据库系统业务模块分类

## 系统概览

基于对数据库设计文档的分析，上海联劝公益基金会的信息系统包含以下10个主要模块：

## 1. 财务支付系统 (Cello)
**文件**: `cello_dataBaseModelDesign.xls`
**表数量**: 116个表
**核心功能**:
- 账户管理（账户表、账户类型表、账户状态表）
- 交易处理（交易流水表、明细流水表）
- 支付管理（订单表、预订单表、成功订单表）
- 发票管理（发票申请表、发票表、发票邮寄情报表）
- 捐赠统计（捐赠统计表、个人年度捐款统计表）
- 月捐/日捐管理（月捐签约表、日捐签约表、扣款记录表）
- 证书管理（成绩证书表、证书模板表）

## 2. CRM客户关系管理系统 (CRM)
**文件**: `crm_dataBaseModelDesign.xls`
**表数量**: 约50个表
**核心功能**:
- 用户信息管理
- 客户关系维护
- 营销活动管理
- 客户分析统计

## 3. 联劝CRM系统 (LQSCRM)
**文件**: `lqscrm_dataBaseModelDesign.xls`
**表数量**: 约40个表
**核心功能**:
- 专门针对联劝业务的CRM功能
- 捐赠者关系管理
- 项目受益人管理

## 4. 小小包子系统 (XXBZ)
**文件**: `xxbz_dataBaseModelDesign.xls`
**表数量**: 52个表
**核心功能**:
- 活动管理（t_xxbz_activity）
- 用户管理（t_xxbz_user）
- 家庭管理（t_xxbz_family、t_xxbz_user_family）
- 团队管理（t_xxbz_team、t_xxbz_teamtype）
- 企业合作（t_xxbz_enterprise）
- 消息通知（t_xxbz_message、t_xxbz_smsRecord）
- 证书管理（t_xxbz_certificate_template）

## 5. 统计分析系统 (Statistics)
**文件**: `statistics_dataBaseModelDesign.xls`
**表数量**: 约30个表
**核心功能**:
- 数据统计分析
- 报表生成
- 业务指标监控
- 决策支持

## 6. 业务管理系统 (Banyan)
**文件**: `banyan_dataBaseModelDesign.xlsx`
**核心功能**:
- 综合业务管理
- 工作流管理
- 文件管理

## 7. CAS认证系统 (CAS)
**文件**: `cas_dataBaseModelDesign.xls`
**核心功能**:
- 统一身份认证
- 用户登录管理
- 权限控制

## 8. HORN系统 (Horn)
**文件**: `horn_dataBaseModelDesign.xls`
**核心功能**:
- 专项业务管理
- 数据处理

## 9. EGG系统 (Egg)
**文件**: `egg_dataBaseModelDesign.xls`
**核心功能**:
- 特定业务模块
- 数据管理

## 10. TAXUS系统 (Taxus)
**文件**: `taxus_dataBaseModelDesign.xls`
**核心功能**:
- 税务相关管理
- 财务合规

## 业务模块特点分析

### 公益基金会特有业务场景
1. **捐赠管理**: 涵盖个人捐赠、企业捐赠、月捐、日捐等多种形式
2. **项目管理**: 公益项目的全生命周期管理
3. **受益人管理**: 项目受益人信息和服务记录
4. **财务透明度**: 详细的财务流水和统计报表
5. **志愿者管理**: 志愿者招募、活动组织、服务记录

### 数据安全和隐私保护
- 用户个人信息加密存储
- 敏感数据访问权限控制
- 审计日志记录

### 数据治理和合规性
- 完整的数据字典和约束
- 标准化的命名规范
- 详细的业务规则定义

## 下一步工作计划
1. 深入分析每个系统的表结构
2. 梳理系统间的数据关系
3. 生成标准化数据字典文档
4. 创建数据关系图谱
