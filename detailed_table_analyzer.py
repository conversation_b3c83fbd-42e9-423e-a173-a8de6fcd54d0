#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的数据库表结构分析工具
专门用于解析上海联劝公益基金会的数据库设计文档
"""

import pandas as pd
import json
import re
from pathlib import Path

class TableStructureAnalyzer:
    def __init__(self):
        self.system_modules = {
            'banyan': '业务管理系统',
            'cas': 'CAS认证系统', 
            'cello': '财务支付系统',
            'crm': 'CRM客户关系管理系统',
            'egg': 'EGG系统',
            'horn': 'HORN系统',
            'lqscrm': '联劝CRM系统',
            'statistics': '统计分析系统',
            'taxus': 'TAXUS系统',
            'xxbz': '小小包子系统'
        }
    
    def parse_table_structure(self, file_path, sheet_name):
        """解析单个表的结构"""
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
            
            table_info = {
                'table_name': sheet_name,
                'chinese_name': '',
                'description': '',
                'fields': [],
                'indexes': [],
                'constraints': [],
                'notes': []
            }
            
            # 查找表的中文名称和描述
            for i in range(min(10, len(df))):
                row = df.iloc[i]
                for j, cell in enumerate(row):
                    if pd.notna(cell):
                        cell_str = str(cell).strip()
                        if '表名' in cell_str or '表说明' in cell_str:
                            # 查找相邻单元格的值
                            if j + 1 < len(row) and pd.notna(row.iloc[j + 1]):
                                table_info['chinese_name'] = str(row.iloc[j + 1]).strip()
                        elif '描述' in cell_str or '用途' in cell_str:
                            if j + 1 < len(row) and pd.notna(row.iloc[j + 1]):
                                table_info['description'] = str(row.iloc[j + 1]).strip()
            
            # 查找字段定义区域
            field_start_row = None
            field_columns = {}
            
            for i in range(len(df)):
                row = df.iloc[i]
                row_text = ' '.join([str(cell) for cell in row if pd.notna(cell)])
                
                # 查找字段定义开始的标志
                if any(keyword in row_text for keyword in ['序号', '字段名', 'Field', 'Column']):
                    field_start_row = i
                    
                    # 确定各列的含义
                    for j, cell in enumerate(row):
                        if pd.notna(cell):
                            cell_str = str(cell).strip().lower()
                            if '序号' in cell_str or 'no' in cell_str:
                                field_columns['index'] = j
                            elif '字段名' in cell_str or 'field' in cell_str:
                                field_columns['name'] = j
                            elif '数据类型' in cell_str or 'type' in cell_str or '类型' in cell_str:
                                field_columns['type'] = j
                            elif '长度' in cell_str or 'length' in cell_str or 'size' in cell_str:
                                field_columns['length'] = j
                            elif '允许空' in cell_str or 'null' in cell_str or '空值' in cell_str:
                                field_columns['nullable'] = j
                            elif '默认值' in cell_str or 'default' in cell_str:
                                field_columns['default'] = j
                            elif '说明' in cell_str or '注释' in cell_str or 'comment' in cell_str or '备注' in cell_str:
                                field_columns['comment'] = j
                            elif '主键' in cell_str or 'primary' in cell_str or 'pk' in cell_str:
                                field_columns['primary_key'] = j
                    break
            
            # 解析字段信息
            if field_start_row is not None and 'name' in field_columns:
                for i in range(field_start_row + 1, len(df)):
                    row = df.iloc[i]
                    
                    # 获取字段名
                    field_name_col = field_columns['name']
                    if field_name_col < len(row):
                        field_name = row.iloc[field_name_col]
                        
                        if pd.notna(field_name) and str(field_name).strip():
                            field_info = {
                                'name': str(field_name).strip(),
                                'type': '',
                                'length': '',
                                'nullable': '',
                                'default': '',
                                'comment': '',
                                'primary_key': False
                            }
                            
                            # 获取其他字段属性
                            for attr, col_idx in field_columns.items():
                                if attr != 'name' and col_idx < len(row):
                                    cell_value = row.iloc[col_idx]
                                    if pd.notna(cell_value):
                                        value_str = str(cell_value).strip()
                                        if attr == 'primary_key':
                                            field_info[attr] = '是' in value_str or 'yes' in value_str.lower() or 'true' in value_str.lower()
                                        else:
                                            field_info[attr] = value_str
                            
                            table_info['fields'].append(field_info)
                        else:
                            # 如果字段名为空，可能已经到了表结构的末尾
                            break
            
            return table_info
            
        except Exception as e:
            return {'table_name': sheet_name, 'error': str(e)}
    
    def analyze_system(self, file_path):
        """分析单个系统的所有表"""
        file_name = Path(file_path).stem
        system_key = file_name.replace('_dataBaseModelDesign', '')
        system_name = self.system_modules.get(system_key, system_key)
        
        print(f"\n正在分析系统: {system_name}")
        print("=" * 60)
        
        try:
            excel_obj = pd.ExcelFile(file_path)
            sheet_names = excel_obj.sheet_names
            
            system_info = {
                'system_key': system_key,
                'system_name': system_name,
                'file_name': file_name,
                'table_count': len(sheet_names),
                'tables': {}
            }
            
            # 分析每个表（限制数量以避免过长处理时间）
            tables_to_analyze = sheet_names[:20]  # 分析前20个表
            
            for sheet_name in tables_to_analyze:
                print(f"  分析表: {sheet_name}")
                table_info = self.parse_table_structure(file_path, sheet_name)
                system_info['tables'][sheet_name] = table_info
                
                if 'error' not in table_info:
                    field_count = len(table_info['fields'])
                    print(f"    -> {field_count} 个字段")
            
            return system_info
            
        except Exception as e:
            print(f"错误: 无法分析文件 {file_path}: {e}")
            return {
                'system_key': system_key,
                'system_name': system_name,
                'file_name': file_name,
                'error': str(e)
            }
    
    def analyze_all_systems(self):
        """分析所有系统"""
        current_dir = Path('.')
        excel_files = list(current_dir.glob('*.xls*'))
        
        all_systems = {}
        
        for excel_file in excel_files:
            system_info = self.analyze_system(excel_file)
            all_systems[system_info['system_key']] = system_info
        
        # 保存详细分析结果
        with open('detailed_database_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(all_systems, f, ensure_ascii=False, indent=2)
        
        # 生成摘要报告
        self.generate_summary_report(all_systems)
        
        return all_systems
    
    def generate_summary_report(self, all_systems):
        """生成摘要报告"""
        print("\n" + "=" * 80)
        print("数据库结构分析摘要报告")
        print("=" * 80)
        
        total_tables = 0
        total_fields = 0
        
        for system_key, system_info in all_systems.items():
            if 'error' in system_info:
                print(f"\n{system_info['system_name']}: 分析错误")
                continue
            
            system_tables = len(system_info['tables'])
            system_fields = 0
            
            for table_name, table_info in system_info['tables'].items():
                if 'error' not in table_info:
                    system_fields += len(table_info['fields'])
            
            total_tables += system_tables
            total_fields += system_fields
            
            print(f"\n{system_info['system_name']}:")
            print(f"  - 表数量: {system_tables}")
            print(f"  - 字段总数: {system_fields}")
            
            # 显示主要表
            main_tables = []
            for table_name, table_info in system_info['tables'].items():
                if 'error' not in table_info and len(table_info['fields']) > 5:
                    main_tables.append((table_name, len(table_info['fields'])))
            
            main_tables.sort(key=lambda x: x[1], reverse=True)
            if main_tables:
                print("  - 主要表:")
                for table_name, field_count in main_tables[:5]:
                    print(f"    * {table_name}: {field_count}字段")
        
        print(f"\n总计:")
        print(f"  - 系统数量: {len(all_systems)}")
        print(f"  - 表总数: {total_tables}")
        print(f"  - 字段总数: {total_fields}")

if __name__ == "__main__":
    analyzer = TableStructureAnalyzer()
    analyzer.analyze_all_systems()
