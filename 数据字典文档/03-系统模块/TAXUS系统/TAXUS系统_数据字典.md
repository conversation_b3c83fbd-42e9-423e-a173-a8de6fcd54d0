# TAXUS系统 数据字典

## 系统概述

**系统名称**: TAXUS系统  
**系统标识**: taxus  
**文件来源**: taxus_dataBaseModelDesign.xls  
**工作表总数**: 143  
**有效表数量**: 140  
**字段总数**: 1656  

## 表结构清单

| 序号 | 表名 | 字段数 | 说明 |
|------|------|--------|------|
| 1 | 订单表 | 65 |  |
| 2 | 联劝网同步订单表 | 64 |  |
| 3 | 发票申请表 | 42 |  |
| 4 | QQ钱包对账单表 | 36 |  |
| 5 | 2015订单表 | 34 |  |
| 6 | 2016订单表 | 34 |  |
| 7 | 微信乐捐对账单表 | 34 |  |
| 8 | 发票表 | 33 |  |
| 9 | 微信滴滴公益对账单表 | 33 |  |
| 10 | 支出结转记录表 | 33 |  |
| 11 | 微公益微信对账单表 | 33 |  |
| 12 | 线下捐赠表 | 30 |  |
| 13 | 账户表 | 29 |  |
| 14 | 微信对账单表 | 28 |  |
| 15 | 字节跳动票据申请信息表 | 26 |  |
| 16 | 支付账单下载配置表 | 25 |  |
| 17 | 移动和包对账单表 | 23 |  |
| 18 | 线下捐赠发票表 | 23 |  |
| 19 | 发票同步信息表 | 23 |  |
| 20 | 微信账单下载配置表 | 21 |  |
| 21 | 支付宝对账单表 | 20 |  |
| 22 | 资金转账记录表 | 20 |  |
| 23 | 银联对账单表 | 20 |  |
| 24 | 自动对账履历表 | 19 |  |
| 25 | 开票人表  | 19 |  |
| 26 | 支付宝账单下载配置表 | 19 |  |
| 27 | 批量退款汇总表 | 19 |  |
| 28 | 财付通对账单表 | 17 |  |
| 29 | 错误发票表 | 17 |  |
| 30 | 物资捐赠同步信息表 | 17 |  |
| 31 | 字节跳动票据申请捐赠明细信息表 | 17 |  |
| 32 | 月统计表 | 16 |  |
| 33 | 域名许可表 | 16 |  |
| 34 | 收入日统计表 | 15 |  |
| 35 | 拨付冻结表 | 15 |  |
| 36 | 资金序列表 | 15 |  |
| 37 | 发票邮寄表 | 15 |  |
| 38 | 京东公益对账单表 | 15 |  |
| 39 | 联劝统筹项目资助退款表 | 15 |  |
| 40 | 退款账单业务处理表 | 15 |  |
| 41 | 字节平台对账单表 | 15 |  |
| 42 | 联劝网月统计表 | 14 |  |
| 43 | 微公益对账单表 | 14 |  |
| 44 | 序列转移表 | 13 |  |
| 45 | 接口访问日志表 | 13 |  |
| 46 | 对账状态表 | 12 |  |
| 47 | 实际打印邮寄表 | 12 |  |
| 48 | （新）公募基金支付设置表  | 12 |  |
| 49 | 明细流水表 | 11 |  |
| 50 | 行政管理费后支付订单的明细流水表 | 11 |  |
| 51 | 行政管理费后支付订单表  | 11 |  |
| 52 | 支出日统计表 | 11 |  |
| 53 | 美团对账单表 | 11 |  |
| 54 | 批量退款明细表 | 11 |  |
| 55 | 小米对账单表 | 11 |  |
| 56 | 行政管理费提取比例表 | 10 |  |
| 57 | 拨付表 | 10 |  |
| 58 | 资金结转记录表 | 10 |  |
| 59 | 发票批次表 | 10 |  |
| 60 | 月累计统计表 | 10 |  |
| 61 | 报名费行政管理费提取比例表 | 10 |  |
| 62 | 商户月度统计表 | 10 |  |
| 63 | 商户月度信息订单关联表 | 10 |  |
| 64 | 日结余表 | 9 |  |
| 65 | 发票打印表 | 9 |  |
| 66 | 年度公益支出统计表 | 9 |  |
| 67 | 错误日志表 | 9 |  |
| 68 | 支付渠道日统计表 | 8 |  |
| 69 | 实体表 | 8 |  |
| 70 | 联劝匿名实名月捐赠统计表 | 8 |  |
| 71 | 联劝月捐赠支付渠道统计表 | 8 |  |
| 72 | 支付类型月统计表 | 8 |  |
| 73 | 个人月度捐款统计表 | 8 |  |
| 74 | 退款记录表 | 8 |  |
| 75 | 账单导入记录表 | 8 |  |
| 76 | 平账补充收入记录表 | 7 |  |
| 77 | 捐赠人变更记录表 | 7 |  |
| 78 | 基金年度账户表 | 7 |  |
| 79 | 联劝月捐赠24小时时间段统计表 | 7 |  |
| 80 | 联劝月筹款报名费统计表 | 7 |  |
| 81 | 月统计记录表 | 7 |  |
| 82 | 月公益支出统计表 | 7 |  |
| 83 | 支付账单读取时间记录 | 7 |  |
| 84 | 支付账单配置变量表 | 7 |  |
| 85 | 第三方平台筹款月统计表 | 7 |  |
| 86 | 交易流水表 | 7 |  |
| 87 | 数据回滚同步表 | 7 |  |
| 88 | 年度账户表 | 7 |  |
| 89 | 年度结余表 | 7 |  |
| 90 | 年度结余表临时表 | 7 |  |
| 91 | 资金转移事件序列表 | 6 |  |
| 92 | 退款报销账户支出预变更记录表 | 6 |  |
| 93 | 子项目表 | 6 |  |
| 94 | 资金池与子资金池匹配表 | 6 |  |
| 95 | 捐赠项配置表 | 6 |  |
| 96 | mq错误信息表 | 6 |  |
| 97 | 对账记录表 | 5 |  |
| 98 | 捐赠统计表 | 5 |  |
| 99 | 特殊扣费项目表 | 5 |  |
| 100 | 收入支出调节表 | 5 |  |
| 101 | 开票内容表 | 5 |  |
| 102 | 支付类型表 | 5 |  |
| 103 | 基础配置表 | 5 |  |
| 104 | 月度账单明细文件表 | 5 |  |
| 105 | 支付宝与腾讯公益开票待核销交易流水表 | 5 |  |
| 106 | 捐赠人ID关联表 | 4 |  |
| 107 | 币种类型表 | 4 |  |
| 108 | 年度支出表 | 4 |  |
| 109 | 短信邮件最后发送时间表 | 4 |  |
| 110 | 订单和发票申请关联表 | 4 |  |
| 111 | 平台表 | 4 |  |
| 112 | solr同步记录表 | 4 |  |
| 113 | 账单账户配置表 | 4 |  |
| 114 | 帐户状态表 | 3 |  |
| 115 | 帐户类型表 | 3 |  |
| 116 | 实体类型表 | 3 |  |
| 117 | 实体网址表 | 3 |  |
| 118 | 发票自取表 | 3 |  |
| 119 | 接口域名密钥表 | 3 |  |
| 120 | 发票定时操作时间记录表 | 3 |  |
| 121 | 线下捐赠审核权限表 | 3 |  |
| 122 | 订单存储过程状态表 | 3 |  |
| 123 | 支付类别名称对应表 | 3 |  |
| 124 | 交易代码表 | 3 |  |
| 125 | 自动转账规则表 | 3 |  |
| 126 | 历史年度收入统计表 | 3 |  |
| 127 | 订单类别名称对应表 | 3 |  |
| 128 | 商户票据关联表 | 3 |  |
| 129 | 日期表 | 2 |  |
| 130 | 历史日期表 | 2 |  |
| 131 | 小时表 | 2 |  |
| 132 | 月度表 | 2 |  |
| 133 | 年度表 | 2 |  |
| 134 | 支出名称表 | 2 |  |
| 135 | 开户标志表 | 2 |  |
| 136 | 订单同步操作时间记录表 | 2 |  |
| 137 | 定时任务开关表 | 2 |  |
| 138 | 异常订单表 | 2 |  |
| 139 | 资金使用序列号生成表 | 1 |  |
| 140 | 领域表 | 0 |  |

## 主要表结构详情

### 订单表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderNo | varchar | 32 | 否 |  | 订单号 |
| 3 | orderName | varchar | 128 | 否 |  | 订单内容(名称) |
| 4 | entityId | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 5 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 6 | toAccount | varchar | 255 | 否 |  | 受捐账户（活动报名费账户） |
| 7 | amount | decimal | 15,2 | 否 |  | 金额 |
| 8 | donateTime | datetime |  | 否 |  | 捐赠时间 |
| 9 | message | varchar | 1024 | 是 |  | 留言 |
| 10 | orderType | tinyint | 2 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 6：资助费 9：现金充值 20：报名费退款 21：捐款退款 25：抽签费退款 26：资助费退 50：调账（订单状态为0） ）（2，3，4在支付系统中有使用， |
| 11 | orderStatus | tinyint | 2 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 12 | invoiceBalance | decimal | 15,2 | 否 |  | 可开发票余额 |
| 13 | phone | varchar | 11 | 是 |  | 手机号码 |
| 14 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 15 | callBackUrl | varchar | 255 | 否 |  | 返回URL |
| 16 | autoCallBackUrl | varchar | 255 | 否 |  | 主动回调URL |
| 17 | searchCode | varchar | 16 | 是 |  | 查询码 |
| 18 | payType | tinyint | 2 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 8：移动和包 9：苏宁易付宝 10：美团钱袋宝 11：微博钱包 80：企业网银（线下）81：个人网银（线下）8 |
| 19 | accessType | tinyint | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加） |
| 20 | tradeDetailId | bigint | 20 | 是 |  | 交易流水ID |
| 21 | serialNo | bigint | 20 | 是 |  | 资金序列号 |
| 22 | invoiceStatus | tinyint | 2 | 否 | 0 | 发票申请状态（0：未申请 1：已申请 2：已开具 3:已邮寄 4：线下已开 5：已自取 6：已退回 7：不能开票 8、9、10个人中心代码里已用 11：项目发起机构统一开票） |
| 23 | bankTradeNo | varchar | 64 | 是 |  | 银行交易流水号 |
| 24 | businessId | varchar | 255 | 是 |  | 商户系统编号（比如活动ID） |
| 25 | templetJsonData | varchar | 512 | 是 |  | 模板数据 |
| 26 | templetType | varchar | 255 | 是 |  | 模板类型 |
| 27 | donateType | tinyint | 2 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：义卖 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 28 | otherSiteReturnData | varchar | 255 | 是 |  | 需要记录的第三方网站返回的特殊业务数据（点融网：点融网的注册用户名） |
| 29 | sourceAccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 30 | checkFlag | tinyint | 2 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，6:支出/提现， 7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 11：线下捐款 12：报名捐 ） |
| 31 | createTime | datetime |  | 是 |  | 订单生成时间（对账成功时间） |
| 32 | name | varchar | 64 | 是 |  | 捐赠者姓名 |
| 33 | ipAddress | varchar | 64 | 是 |  | ip地址 |
| 34 | ipType | int | 1 | 是 | 0 | ip类型（0：ipv4 1：ipv6） |
| 35 | inCount | int | 11 | 是 | 0 | 合并账单数 |
| 36 | fundEntityId | int | 11 | 是 | 0 | 资金池 |
| 37 | poolEntityId | int | 11 | 是 | 0 | 子资金池 |
| 38 | intItem3 | int | 11 | 是 | 0 | 预留的项目3 |
| 39 | intItem4 | int | 11 | 是 | 0 | 预留的项目4 |
| 40 | frozenStatus | int | 11 | 是 | 0 | 0：未冻结；1：已冻结 |
| 41 | inCount | int | 11 | 是 | 1 | 合并账单数 |
| 42 | dateItem1 | datetime |  | 是 |  | 预留日期项目1 |
| 43 | dateItem2 | datetime |  | 是 |  | 预留日期项目2 |
| 44 | dateItem3 | datetime |  | 是 |  | 预留日期项目3 |
| 45 | dateItem4 | datetime |  | 是 |  | 预留日期项目4 |
| 46 | strItem1 | varchar | 64 | 是 |  | 预留字符串1 |
| 47 | strItem2 | varchar | 64 | 是 |  | 预留字符串2 |
| 48 | strItem3 | varchar | 64 | 是 |  | 预留字符串3 |
| 49 | strItem4 | varchar | 64 | 是 |  | 预留字符串4 |
| 50 | strItem5 | varchar | 128 | 是 |  | 预留字符串5 |
| 51 | strItem6 | varchar | 128 | 是 |  | 预留字符串6 |
| 52 | strItem7 | varchar | 128 | 是 |  | 预留字符串7 |
| 53 | strItem8 | varchar | 128 | 是 |  | 预留字符串8 |
| 54 | strItem9 | varchar | 255 | 是 |  | 预留字符串9 |
| 55 | strItem10 | varchar | 255 | 是 |  | 预留字符串10 |
| 56 | strItem11 | varchar | 255 | 是 |  | 预留字符串11 |
| 57 | strItem12 | varchar | 255 | 是 |  | 预留字符串12 |
| 58 | amountItem1 | decimal | 15,2 | 是 | 0 | 预留金额1 |
| 59 | amountItem2 | decimal | 15,2 | 是 | 0 | 预留金额2 |
| 60 | amountItem3 | decimal | 15,2 | 是 | 0 | 预留金额3 |
| 61 | amountItem4 | decimal | 15,2 | 是 | 0 | 预留金额4 |
| 62 | doubleItem1 | double |  | 是 | 0 | 预留双精度1 |
| 63 | doubleItem2 | double |  | 是 | 0 | 预留双精度2 |
| 64 | doubleItem3 | double |  | 是 | 0 | 预留双精度3 |
| 65 | doubleItem4 | double |  | 是 | 0 | 预留双精度4 |

### 联劝网同步订单表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderno | varchar | 32 | 否 |  | 订单号 |
| 3 | ordername | varchar | 128 | 否 |  | 订单内容(名称) |
| 4 | entityid | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 5 | fromaccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 6 | toaccount | varchar | 32 | 否 |  | 受捐账户（活动报名费账户） |
| 7 | amount | decimal | 15,2 | 否 |  | 金额 |
| 8 | donatetime | datetime |  | 否 |  | 捐赠时间 |
| 9 | message | varchar | 1024 | 是 |  | 留言 |
| 10 | ordertype | int | 1 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 6：资助费 9：现金充值 20：报名费退款 21：捐款退款 25：抽签费退款 26：资助费退 50：调账（订单状态为0） ）（2，3，4在支付系统中有使用， |
| 11 | orderstatus | int | 1 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 12 | phone | varchar | 11 | 是 |  | 手机号码 |
| 13 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 14 | callbackurl | varchar | 255 | 否 |  | 返回URL |
| 15 | autocallbackurl | varchar | 255 | 否 |  | 主动回调URL |
| 16 | searchcode | varchar | 16 | 是 |  | 查询码 |
| 17 | paytype | int | 1 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 80：企业网银（线下）81：个人网银（线下）82：财付通（线下）83：支付宝（线下）84：现金 85：京东众筹 |
| 18 | accesstype | int | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加） |
| 19 | tradedetailid | bigint | 20 | 是 |  | 交易流水ID |
| 20 | serialno | bigint | 20 | 是 |  | 资金序列号 |
| 21 | banktradeno | varchar | 64 | 是 |  | 银行交易流水号 |
| 22 | businessid | varchar | 255 | 是 |  | 商户系统编号（比如活动ID） |
| 23 | templetjsondata | varchar | 512 | 是 |  | 模板数据 |
| 24 | templettype | varchar | 255 | 是 |  | 模板类型 |
| 25 | donatetype | int | 1 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：义卖 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 26 | othersitereturndata | varchar | 255 | 是 |  | 需要记录的第三方网站返回的特殊业务数据（点融网：点融网的注册用户名） |
| 27 | sourceaccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 28 | checkflag | int | 1 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，6:支出/提现， 7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 11：线下捐款 12：报名捐 ） |
| 29 | createtime | datetime |  | 是 |  | 订单生成时间 |
| 30 | name | varchar | 64 | 是 |  | 捐赠者姓名 |
| 31 | ipaddress | varchar | 64 | 是 |  | ip地址 |
| 32 | iptype | int | 1 | 是 | 0 | ip类型（0：ipv4 1：ipv6） |
| 33 | intitem1 | int | 11 | 是 | 0 | 预留的项目1 |
| 34 | intitem2 | int | 11 | 是 | 0 | 预留的项目2 |
| 35 | intitem3 | int | 11 | 是 | 0 | 预留的项目3 |
| 36 | intitem4 | int | 11 | 是 | 0 | 预留的项目4 |
| 37 | intitem5 | int | 11 | 是 | 0 | 0：未冻结；1：已冻结 |
| 38 | intitem6 | int | 11 | 是 | 1 | 合并账单数 |
| 39 | dateitem1 | datetime |  | 是 |  | 预留日期项目1 |
| 40 | dateitem2 | datetime |  | 是 |  | 预留日期项目2 |
| 41 | dateitem3 | datetime |  | 是 |  | 预留日期项目3 |
| 42 | dateitem4 | datetime |  | 是 |  | 预留日期项目4 |
| 43 | stritem1 | varchar | 64 | 是 |  | 预留字符串1 |
| 44 | stritem2 | varchar | 64 | 是 |  | 预留字符串2 |
| 45 | stritem3 | varchar | 64 | 是 |  | 预留字符串3 |
| 46 | stritem4 | varchar | 64 | 是 |  | 预留字符串4 |
| 47 | stritem5 | varchar | 128 | 是 |  | 预留字符串5 |
| 48 | stritem6 | varchar | 128 | 是 |  | 预留字符串6 |
| 49 | stritem7 | varchar | 128 | 是 |  | 预留字符串7 |
| 50 | stritem8 | varchar | 128 | 是 |  | 预留字符串8 |
| 51 | stritem9 | varchar | 255 | 是 |  | 预留字符串9 |
| 52 | stritem10 | varchar | 255 | 是 |  | 预留字符串10 |
| 53 | stritem11 | varchar | 255 | 是 |  | 预留字符串11 |
| 54 | stritem12 | varchar | 255 | 是 |  | 预留字符串12 |
| 55 | amountitem1 | decimal | 15,2 | 是 | 0 | 预留金额1 |
| 56 | amountitem2 | decimal | 15,2 | 是 | 0 | 预留金额2 |
| 57 | amountitem3 | decimal | 15,2 | 是 | 0 | 预留金额3 |
| 58 | amountitem4 | decimal | 15,2 | 是 | 0 | 预留金额4 |
| 59 | doubleitem1 | double |  | 是 | 0 | 预留双精度1 |
| 60 | doubleitem2 | double |  | 是 | 0 | 预留双精度2 |
| 61 | doubleitem3 | double |  | 是 | 0 | 预留双精度3 |
| 62 | doubleitem4 | double |  | 是 | 0 | 预留双精度4 |
| 63 | flag | int | 1 | 是 | 0 | 0:正常 1：异常 |
| 64 | errmsg | text |  | 是 |  | 错误信息 |

### 发票申请表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | invoiceId | int | 11 | 是 |  | 发票ID |
| 3 | postId | int | 11 | 否 |  | 邮寄情报ID |
| 4 | invoiceTitle | varchar | 128 | 否 |  | 发票抬头 |
| 5 | donateProjectName | text | 0 | 否 |  | 捐赠项目 |
| 6 | amount | decimal | 15,2 | 否 |  | 发票金额 |
| 7 | handlePerson | varchar | 32 | 是 |  | 开票人姓名 |
| 8 | verfiedPerson | varchar | 32 | 是 |  | 复核人姓名 |
| 9 | status | int | 1 | 否 | 0 | 申请处理状态（0：待开 1：正常（已开具）；2：开票中 3：开票失败；4：审核中；5：审核不通过） |
| 10 | applyTime | datetime |  | 否 |  | 发票申请时间 |
| 11 | remark | text | 0 | 是 |  | 备注 |
| 12 | applyCode | varchar | 8 | 否 |  | 申请编号（联劝网发票申请同步过来的，基金会发票申请不需要） |
| 13 | invoiceType | tinyint | 4 |  |  | 票据类型（0：匿名；1：个人；2：企业；） |
| 14 | cardType | tinyint | 4 |  |  | 证件号类型（1：身份证；2：护照号） |
| 15 | creditCode | varchar | 64 |  |  | 统一社会信用代码 |
| 16 | isNew | tinyint | 4 | 否 | 2 | 是否是新发票（1：不是；2：是） |
| 17 | itemId | bigint | 20 |  |  | 票据内容id |
| 18 | receiveType | tinyint | 4 |  |  | 收款方式:1 现金,2 转账,3 公共支付 平台. 4 POS 交款 |
| 19 | errorMsg | text | 0 |  |  | 错误信息 |
| 20 | invoiceTime | datetime | 0 |  |  | 发票时间 |
| 21 | serialNumber | varchar | 64 |  |  | 发票工程编号 |
| 22 | outFlag | tinyint | 4 | 否 | 0 | 其他申请来源（0：基金会电子票据；1：支付宝；2：腾讯；3：先收款后开票；4：字节跳动；） |
| 23 | syncFlag | tinyint | 4 | 否 | 0 | 同步标记，其他申请来源用（0：未同步；1：已同步；2：同步失败；3：作废已同步；4：作废同步失败） |
| 24 | syncMsg | text | 0 |  |  | 同步票据失败说明 |
| 25 | uid | varchar | 256 |  |  | 支付宝参数：用户ID |
| 26 | tradeNo | varchar | 256 |  |  | 支付宝参数：交易号 |
| 27 | tradeDate | datetime | 0 |  |  | 支付宝参数：交易日期 |
| 28 | ngoName | varchar | 256 |  |  | 支付宝参数：机构名称 |
| 29 | ngoId | varchar | 256 |  |  | 支付宝参数：机构ID |
| 30 | backInvoiceUrl | varchar | 256 |  |  | 支付宝同步回传发票地址（无实际用处，仅记录） |
| 31 | iuid | varchar | 256 |  |  | 腾讯参数：开票号（发票申请id） |
| 32 | openindex | int | 11 |  |  | 腾讯参数：重开序号 |
| 33 | uscc | varchar | 256 |  |  | 腾讯参数：统一社会信用代码（企业开票用） |
| 34 | pid | int | 11 |  |  | 腾讯参数：开票项目 ID |
| 35 | fundid | int | 11 |  |  | 腾讯参数：开票公募 ID |
| 36 | userid | varchar | 256 |  |  | 腾讯参数：捐款人 ID |
| 37 | openid | varchar | 256 |  |  | 腾讯参数：小程序 openid |
| 38 | number | int | 11 |  |  | 腾讯参数：捐款笔数 |
| 39 | tradeNos | text | 0 |  |  | 腾讯流水号,拼接 |
| 40 | details | text | 0 |  |  | 腾讯参数：捐款明细JSONArray格式 |
| 41 | reopenFlg | tinyint | 0 | 否 |  | 是否已经重开（0：没有重开；1：已经重开；2：商家未重开） |
| 42 | gybbFlg | tinyint | 0 | 否 |  | 是否是公益宝贝或者消费捐开票（0：否；1：是；） |

### QQ钱包对账单表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderTime | varchar | 32 | 否 |  | 交易时间 |
| 3 | merchantsNo | varchar | 64 |  |  | 商户号 |
| 4 | merchantsAppId | varchar | 64 |  |  | 商户APPID |
| 5 | childMerchantsNo | varchar | 64 |  |  | 子商户号 |
| 6 | childMerchantsAppId | varchar | 64 |  |  | 子商户APPID |
| 7 | userMark | varchar | 64 |  |  | 用户标识 |
| 8 | deviceNo | varchar | 64 |  |  | 设备号 |
| 9 | paymentScenario | varchar | 64 |  |  | 支付场景 |
| 10 | merchantsOrderNo | varchar | 64 | 否 |  | 商户订单号 |
| 11 | qqOrderNo | varchar | 64 | 否 |  | QQ钱包订单号 |
| 12 | payBank | varchar | 64 |  |  | 付款银行 |
| 13 | currentType | varchar | 16 |  |  | 货币种类 |
| 14 | allAmount | decimal | 15，2 | 否 |  | 总金额 |
| 15 | merchantCouponAmount | decimal | 15，2 |  |  | 商户优惠金额(元) |
| 16 | merchantCollectAmount | decimal | 15，2 |  |  | 商户应收金额(元) |
| 17 | qqDiscountAmount | decimal | 15，2 |  |  | QQ钱包优惠金额(元) |
| 18 | userPayAmount | decimal | 15，2 |  |  | 用户支付金额(元) |
| 19 | tradeStatus | varchar | 64 |  |  | 交易状态 |
| 20 | refundSubmitTime | varchar | 0 |  |  | 退款提交时间 |
| 21 | refundMerchantsOrderNo | varchar | 64 |  |  | 商户退款订单号 |
| 22 | refundQqOrderNo | varchar | 64 |  |  | QQ钱包退款订单号 |
| 23 | refundAmount | decimal | 15，,2 |  |  | 退款金额(元) |
| 24 | refundCouponAmount | decimal | 15,2 |  |  | 退还QQ钱包优惠金额(元) |
| 25 | refundStatus | varchar | 64 |  |  | 退款状态 |
| 26 | refundSuccessTime | varchar | 0 |  |  | 退款成功时间 |
| 27 | refundType | varchar | 64 |  |  | 退款方式 |
| 28 | orderName | varchar | 64 |  |  | 商品名称 |
| 29 | merchantsData | varchar | 255 |  |  | 商户数据包 |
| 30 | fee | decimal | 15，,2 |  |  | 手续费 |
| 31 | rate | varchar | 16 |  |  | 费率 |
| 32 | checkFlag | tinyint | 2 | 否 |  | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 12：处理8未成功的临时值） |
| 33 | insertTime | datetime | 0 | 否 |  | 导入时间 |
| 34 | mergeOrders | mediumtext | 0 |  |  | 合并订单的信息集合 |
| 35 | inCount | int | 11 |  |  | 合并订单数 |
| 36 | endTime | datetime | 0 |  |  | 合并订单的最后一笔的捐赠时间 |

### 2015订单表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderNo | varchar | 32 | 否 |  | 订单号 |
| 3 | orderName | varchar | 128 | 否 |  | 订单内容(名称) |
| 4 | entityId | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 5 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 6 | toAccount | varchar | 255 | 否 |  | 受捐账户（活动报名费账户） |
| 7 | amount | decimal | 15,2 | 否 |  | 金额 |
| 8 | donateTime | datetime |  | 否 |  | 捐赠时间 |
| 9 | message | varchar | 1024 | 是 |  | 留言 |
| 10 | orderType | tinyint | 2 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 6：资助费 9：现金充值 20：报名费退款 21：捐款退款 25：抽签费退款 26：资助费退 50：调账（订单状态为0） ）（2，3，4在支付系统中有使用， |
| 11 | orderStatus | tinyint | 2 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 12 | invoiceBalance | decimal | 15,2 | 否 |  | 可开发票余额 |
| 13 | phone | varchar | 11 | 是 |  | 手机号码 |
| 14 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 15 | callBackUrl | varchar | 255 | 否 |  | 返回URL |
| 16 | autoCallBackUrl | varchar | 255 | 否 |  | 主动回调URL |
| 17 | searchCode | varchar | 16 | 是 |  | 查询码 |
| 18 | payType | tinyint | 2 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 80：企业网银（线下）81：个人网银（线下）82：财付通（线下）83：支付宝（线下）84：现金 85：京东众筹 |
| 19 | accessType | tinyint | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加） |
| 20 | tradeDetailId | bigint | 20 | 是 |  | 交易流水ID |
| 21 | serialNo | bigint | 20 | 是 |  | 资金序列号 |
| 22 | invoiceStatus | tinyint | 2 | 否 | 0 | 发票申请状态（0：未申请 1：已申请 2：已开具 3:已邮寄 4：线下已开 5：已自取 6：已退回 7：不能开票 8、9、10个人中心代码里已用 11：项目发起机构统一开票） |
| 23 | bankTradeNo | varchar | 64 | 是 |  | 银行交易流水号 |
| 24 | businessId | varchar | 255 | 是 |  | 商户系统编号（比如活动ID） |
| 25 | templetJsonData | varchar | 512 | 是 |  | 模板数据 |
| 26 | templetType | varchar | 255 | 是 |  | 模板类型 |
| 27 | donateType | tinyint | 2 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：义卖 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 28 | otherSiteReturnData | varchar | 255 | 是 |  | 需要记录的第三方网站返回的特殊业务数据（点融网：点融网的注册用户名） |
| 29 | sourceAccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 30 | checkFlag | tinyint | 2 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，6:支出/提现， 7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 11：线下捐款 12：报名捐 ） |
| 31 | createTime | datetime |  | 是 |  | 订单生成时间 |
| 32 | name | varchar | 64 | 是 |  | 捐赠者姓名 |
| 33 | fundEntityId | int | 11 |  |  | 资金池 |
| 34 | poolEntityId | int | 11 |  |  | 子资金池 |

### 2016订单表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderNo | varchar | 32 | 否 |  | 订单号 |
| 3 | orderName | varchar | 128 | 否 |  | 订单内容(名称) |
| 4 | entityId | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 5 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 6 | toAccount | varchar | 255 | 否 |  | 受捐账户（活动报名费账户） |
| 7 | amount | decimal | 15,2 | 否 |  | 金额 |
| 8 | donateTime | datetime |  | 否 |  | 捐赠时间 |
| 9 | message | varchar | 1024 | 是 |  | 留言 |
| 10 | orderType | tinyint | 2 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 6：资助费 9：现金充值 20：报名费退款 21：捐款退款 25：抽签费退款 26：资助费退 50：调账（订单状态为0） ）（2，3，4在支付系统中有使用， |
| 11 | orderStatus | tinyint | 2 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 12 | invoiceBalance | decimal | 15,2 | 否 |  | 可开发票余额 |
| 13 | phone | varchar | 11 | 是 |  | 手机号码 |
| 14 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 15 | callBackUrl | varchar | 255 | 否 |  | 返回URL |
| 16 | autoCallBackUrl | varchar | 255 | 否 |  | 主动回调URL |
| 17 | searchCode | varchar | 16 | 是 |  | 查询码 |
| 18 | payType | tinyint | 2 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 80：企业网银（线下）81：个人网银（线下）82：财付通（线下）83：支付宝（线下）84：现金 85：京东众筹 |
| 19 | accessType | tinyint | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加） |
| 20 | tradeDetailId | bigint | 20 | 是 |  | 交易流水ID |
| 21 | serialNo | bigint | 20 | 是 |  | 资金序列号 |
| 22 | invoiceStatus | tinyint | 2 | 否 | 0 | 发票申请状态（0：未申请 1：已申请 2：已开具 3:已邮寄 4：线下已开 5：已自取 6：已退回 7：不能开票 8、9、10个人中心代码里已用 11：项目发起机构统一开票） |
| 23 | bankTradeNo | varchar | 64 | 是 |  | 银行交易流水号 |
| 24 | businessId | varchar | 255 | 是 |  | 商户系统编号（比如活动ID） |
| 25 | templetJsonData | varchar | 512 | 是 |  | 模板数据 |
| 26 | templetType | varchar | 255 | 是 |  | 模板类型 |
| 27 | donateType | tinyint | 2 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：义卖 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 28 | otherSiteReturnData | varchar | 255 | 是 |  | 需要记录的第三方网站返回的特殊业务数据（点融网：点融网的注册用户名） |
| 29 | sourceAccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 30 | checkFlag | tinyint | 2 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，6:支出/提现， 7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 11：线下捐款 12：报名捐 ） |
| 31 | createTime | datetime |  | 是 |  | 订单生成时间 |
| 32 | name | varchar | 64 | 是 |  | 捐赠者姓名 |
| 33 | fundEntityId | int | 11 |  |  | 资金池 |
| 34 | poolEntityId | int | 11 |  |  | 子资金池 |

### 微信乐捐对账单表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderTime | varchar | 32 | 否 |  | 交易时间 |
| 3 | publicId | varchar | 32 | 是 |  | 公众账号ID |
| 4 | merchantsNo | varchar | 32 | 是 |  | 商户号 |
| 5 | childMerchantsNo | varchar | 32 | 是 |  | 子商户号 |
| 6 | deviceNo | varchar | 64 | 是 |  | 设备号 |
| 7 | weixinOrderNo | varchar | 32 | 否 |  | 微信订单号 |
| 8 | merchantsOrderNo | varchar | 32 | 否 |  | 商户订单号 |
| 9 | userMark | varchar | 32 | 是 |  | 用户标识 |
| 10 | tradeType | varchar | 64 | 是 |  | 交易类型 |
| 11 | tradeStatus | varchar | 32 | 是 |  | 交易状态 |
| 12 | payBank | varchar | 32 | 是 |  | 付款银行 |
| 13 | currentType | varchar | 16 | 是 |  | 货币种类 |
| 14 | allAmount | decimal | 15,2 | 否 |  | 总金额 |
| 15 | enterpriseHongbaoAmount | decimal | 15,2 | 否 |  | 企业红包金额 |
| 16 | weixinRefundOrderNo | varchar | 32 | 是 |  | 微信退款单号 |
| 17 | merchantsRefundOrderNo | varchar | 32 | 是 |  | 商户退款单号 |
| 18 | refundAmount | decimal | 15,2 | 是 |  | 退款金额 |
| 19 | enterpriseHongbaoRefundAmount | decimal | 15,2 | 是 |  | 企业红包退款金额 |
| 20 | refundType | varchar | 32 | 是 |  | 退款类型 |
| 21 | refundStatus | varchar | 32 | 是 |  | 退款状态 |
| 22 | orderName | varchar | 64 | 是 |  | 商品名称 |
| 23 | merchantsData | varchar | 255 | 是 |  | 商户数据包 |
| 24 | fee | decimal | 15,2 | 是 |  | 手续费 |
| 25 | rate | varchar | 16 | 是 |  | 费率 |
| 26 | checkFlag | tinyint | 1 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 12：处理8未成功的临时值） |
| 27 | insertTime | dateTime |  | 否 |  | 导入时间 |
| 28 | mergeOrders | mediumtext | 0 | 是 |  | 合并订单的信息集合 |
| 29 | inCount | int | 11 | 否 | 1 | 合并订单数 |
| 30 | endTime | datetime | 0 | 是 |  | 合并订单的最后一笔的捐赠时间 |
| 31 | payAmount | decimal | 15,2 | 是 |  | 应结订单金额 |
| 32 | applyRefundAmount | decimal | 15,2 | 是 |  | 申请退款金额 |
| 33 | rateNote | varchar | 500 | 是 |  | 费率备注 |
| 34 | wechat | varchar | 255 | 是 |  | 微信商户号 |

### 发票表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | batchId | int | 11 | 否 |  | 发票批次ID |
| 3 | invoiceCode | varchar | 16 | 否 |  | 发票编号 |
| 4 | status | int | 2 | 否 | 0 | 发票状态（0：待开 1：正常（已开具） 2：作废；4：作废审核中） |
| 5 | printTime | datetime |  | 是 |  | 发票打印时间（发票开具时间） |
| 6 | invalidTime | datetime |  | 是 |  | 发票作废时间 |
| 7 | invalidUser | varchar | 32 | 是 |  | 发票作废操作者 |
| 8 | invoiceTitle | varchar | 128 | 是 |  | 发票抬头 |
| 9 | invoiceTime | datetime |  | 是 |  | 发票时间 |
| 10 | item1 | varchar | 128 | 是 |  | 捐赠项目1 |
| 11 | amount1 | decimal | 15,2 | 是 |  | 发票金额1 |
| 12 | item2 | varchar | 128 | 是 |  | 捐赠项目2 |
| 13 | amount2 | decimal | 15,2 | 是 |  | 发票金额2 |
| 14 | item3 | varchar | 128 | 是 |  | 捐赠项目3 |
| 15 | amount3 | decimal | 15,2 | 是 |  | 发票金额3 |
| 16 | handlePerson | varchar | 32 | 是 |  | 开票人姓名 |
| 17 | verfiedPerson | varchar | 32 | 是 |  | 复核人姓名 |
| 18 | remark | varchar | 256 | 是 |  | 备注 |
| 19 | postId | int | 11 | 是 |  | 实际打印邮寄情报表的ID |
| 20 | invalidInvoiceId | int | 11 | 是 |  | 替换的作废发票ID |
| 21 | invalidReason | varchar | 255 | 是 |  | 作废理由 |
| 22 | selfGetId | int | 11 | 是 |  | 自取ID |
| 23 | serialNumber | varchar | 64 |  |  | 发票工程编号 |
| 24 | invoiceImg | varchar | 255 |  |  | 发票图片 |
| 25 | invoicePdf | varchar | 255 |  |  | 发票pdf |
| 26 | errorMsg | text | 0 |  |  | 错误信息 |
| 27 | isSend | tinyint | 4 |  |  | 是否发送短信（0：未发送；1：已发送；2：无需发送） |
| 28 | shortUrl | varchar | 500 |  |  | 短链接 |
| 29 | invoiceBantchCode | varchar | 255 |  |  | 票据代码 |
| 30 | verify | varchar | 64 |  |  | 校验码 |
| 31 | transformPdf | varchar | 255 |  |  | png图片转为pdf格式 |
| 32 | invoicePdfOss | varchar | 255 |  |  | 发票PDF文件保存到OSS存储 |
| 33 | pdfOssFlag | int | 0 |  |  | 发票PDF文件保存到OSS存储标志（0：未同步；1：已同步） |

### 微信滴滴公益对账单表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderTime | varchar | 32 | 否 |  | 交易时间 |
| 3 | publicId | varchar | 32 | 是 |  | 公众账号ID |
| 4 | merchantsNo | varchar | 32 | 是 |  | 商户号 |
| 5 | childMerchantsNo | varchar | 32 | 是 |  | 子商户号 |
| 6 | deviceNo | varchar | 64 | 是 |  | 设备号 |
| 7 | weixinOrderNo | varchar | 32 | 否 |  | 微信订单号 |
| 8 | merchantsOrderNo | varchar | 32 | 否 |  | 商户订单号 |
| 9 | userMark | varchar | 32 | 是 |  | 用户标识 |
| 10 | tradeType | varchar | 64 | 是 |  | 交易类型 |
| 11 | tradeStatus | varchar | 32 | 是 |  | 交易状态 |
| 12 | payBank | varchar | 32 | 是 |  | 付款银行 |
| 13 | currentType | varchar | 16 | 是 |  | 货币种类 |
| 14 | allAmount | decimal | 15 | 否 |  | 总金额 |
| 15 | cashCouponAmount | decimal | 15 | 否 |  | 代金券金额 |
| 16 | weixinRefundOrderNo | varchar | 32 | 是 |  | 微信退款单号 |
| 17 | merchantsRefundOrderNo | varchar | 64 | 是 |  | 商户退款单号 |
| 18 | refundAmount | decimal | 15 | 是 |  | 退款金额 |
| 19 | cashCouponRefundAmount | decimal | 15 | 是 |  | 充值券退款金额 |
| 20 | refundType | varchar | 32 | 是 |  | 退款类型 |
| 21 | refundStatus | varchar | 32 | 是 |  | 退款状态 |
| 22 | orderName | varchar | 64 | 是 |  | 商品名称 |
| 23 | merchantsData | varchar | 255 | 是 |  | 商户数据包 |
| 24 | fee | decimal | 15 | 是 |  | 手续费 |
| 25 | rate | varchar | 16 | 是 |  | 费率 |
| 26 | payAmount | decimal | 15 | 否 | 0 | 应结订单金额 |
| 27 | applyRefundAmount | decimal | 15 | 否 |  | 申请退款金额 |
| 28 | rateNote | varchar | 255 | 是 |  | 费率备注 |
| 29 | checkFlag | int | 2 | 否 | 1 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配） |
| 30 | insertTime | datetime | 0 | 是 |  | 导入时间 |
| 31 | mergeOrders | mediumtext | 0 | 是 |  | 合并订单的信息集合 |
| 32 | inCount | int | 11 | 是 |  | 合并订单数 |
| 33 | endTime | datetime | 0 | 是 |  | 合并订单的最后一笔的捐赠时间 |

### 支出结转记录表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | isSerial | tinyint | 4 |  |  | 是否有流水号（0：没有；1：有；） |
| 3 | serialNo | varchar | 64 |  |  | 拨付流水号 |
| 4 | outFromName | varchar | 64 |  |  | 支出对象 |
| 5 | outFromGuid | varchar | 64 |  |  | 支出对象guid |
| 6 | outToProjectName | varchar | 64 |  |  | 支出用途 |
| 7 | outToProjectGuid | varchar | 64 |  |  | 支出用途guid |
| 8 | outAmount | decimal | 15 |  |  | 支出金额 |
| 9 | outTime | date | 0 |  |  | 支出时间 |
| 10 | useRemark | varchar | 64 |  |  | 调整用途 |
| 11 | useRemarkGuid | varchar | 64 |  |  | 调整用途guid |
| 12 | costId | bigint | 20 |  |  | 支出流水号为行政支出时选择的报销清单id |
| 13 | payeeAccount | varchar | 64 |  |  | 用户收款账户（报销用） |
| 14 | oldFromType | tinyint | 4 |  |  | 调整前支出类型（1：公益支出；2：行政支出；3：服务支出；） |
| 15 | oldFundType | tinyint | 4 |  |  | 调整前业务类型（10：企业公益合作；12：公益专项基金；17：公益专项合作；19：企业专项基金；21：联劝统筹；23：联劝网合作；25：捐赠人建议基金） |
| 16 | oldFromField | tinyint | 4 |  |  | 调整前资助领域（业务侧基金/合作的领域） |
| 17 | oldFromName | varchar | 64 |  |  | 调整前资金池名称 |
| 18 | oldFromGuid | varchar | 64 |  |  | 调整前资金池guid |
| 19 | oldFromSonName | varchar | 64 |  |  | 调整前子资金池名称 |
| 20 | oldFromSonGuid | varchar | 64 |  |  | 调整前子资金池guid |
| 21 | fromType | tinyint | 4 |  |  | 调整后支出类型（1：公益支出；2：行政支出；3：服务支出；） |
| 22 | fromName | varchar | 64 |  |  | 调整后资金池名称 |
| 23 | fromGuid | varchar | 64 |  |  | 调整后资金池guid |
| 24 | fromSonName | varchar | 64 |  |  | 调整后子资金池名称 |
| 25 | fromSonGuid | varchar | 64 |  |  | 调整后子资金池guid |
| 26 | fundType | tinyint | 4 |  |  | 调整后业务类型（10：企业公益合作；12：公益专项基金；17：公益专项合作；19：企业专项基金；21：联劝统筹；23：联劝网合作；25：捐赠人建议基金） |
| 27 | fromField | tinyint | 4 |  |  | 调整后资助领域（业务侧基金/合作的领域） |
| 28 | adjustTime | datetime | 0 |  |  | 调整时间 |
| 29 | isRefund | tinyint | 4 |  |  | 是否退款（1：是；2：不是；） |
| 30 | adjustAmount | decimal | 15 |  |  | 调整金额 |
| 31 | createTime | datetime | 0 |  |  | 创建时间 |
| 32 | activityCostId | bigint |  |  |  | 支出流水号为联劝统筹时选择的费用清单id |
| 33 | fromSonType | tinyint |  |  |  | 账户属性（0：匿名捐款 1：实名捐款 2：筹款 3：报名费 4:配捐(第三方平台) 5：配捐(企业赞助商)  6：抽签费 7：行政经费 8:借款 9：其他 10：可拨付经费 11：接受拨付 12：年度 |

