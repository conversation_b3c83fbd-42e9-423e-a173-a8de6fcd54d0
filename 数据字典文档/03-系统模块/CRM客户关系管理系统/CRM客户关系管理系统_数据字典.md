# CRM客户关系管理系统 数据字典

## 系统概述

**系统名称**: CRM客户关系管理系统  
**系统标识**: crm  
**文件来源**: crm_dataBaseModelDesign.xls  
**工作表总数**: 148  
**有效表数量**: 143  
**字段总数**: 1678  

## 表结构清单

| 序号 | 表名 | 字段数 | 说明 |
|------|------|--------|------|
| 1 | 捐赠人表 | 57 |  |
| 2 | 捐赠人重复表 | 57 |  |
| 3 | 捐赠人临时表  | 56 |  |
| 4 | 机构表 | 49 |  |
| 5 | 机构信息修改表 | 37 |  |
| 6 | 用户表 | 30 |  |
| 7 | 机构新闻表 | 29 |  |
| 8 | 公众号用户消息表 | 27 |  |
| 9 | 邮件发送队列表 | 26 |  |
| 10 | 发票表 | 26 |  |
| 11 | 消费记录表  | 25 |  |
| 12 | 部门表 | 24 |  |
| 13 | 黑名单表 | 22 |  |
| 14 | 公众号用户表 | 18 |  |
| 15 | 机构信息展示编辑表  | 18 |  |
| 16 | 机构信息表 | 18 |  |
| 17 | 短信发送队列表 | 18 |  |
| 18 | 模板表  | 18 |  |
| 19 | 菜单表 | 17 |  |
| 20 | 推广传播表 | 17 |  |
| 21 | 画像标签表 | 17 |  |
| 22 | 标签表 | 16 |  |
| 23 | 邮件任务 | 16 |  |
| 24 | 短信任务  | 16 |  |
| 25 | 拖拽ui表 | 16 |  |
| 26 | 模块表 | 16 |  |
| 27 | 产品价格表 | 16 |  |
| 28 | 业务消息通知表 | 15 |  |
| 29 | 公众号素材表 | 14 |  |
| 30 | 机构账户表 | 14 |  |
| 31 | 维护子计划 | 14 |  |
| 32 | 电话任务 | 14 |  |
| 33 | 其他任务  | 14 |  |
| 34 | 电话联络队列表  | 14 |  |
| 35 | 角色表 | 14 |  |
| 36 | 产品套餐表 | 14 |  |
| 37 | 签到码表 | 14 |  |
| 38 | 海报内容位置表 | 14 |  |
| 39 | 联络任务 | 13 |  |
| 40 | 邮件发送统计表 | 13 |  |
| 41 | 其他联络队列表   | 13 |  |
| 42 | 传播访问日志表 | 13 |  |
| 43 | 微信第三方平台配置表 | 12 |  |
| 44 | 登录日志表 | 12 |  |
| 45 | 标签捐赠人关系表 | 12 |  |
| 46 | 渠道表 | 12 |  |
| 47 | 附件表 | 12 |  |
| 48 | 微信模板发送统计表 | 11 |  |
| 49 | 公众号模板消息发送记录表 | 11 |  |
| 50 | 用户分组规则 | 11 |  |
| 51 | 短信模板 | 11 |  |
| 52 | 短信发送统计表 | 11 |  |
| 53 | 联络记录 | 11 |  |
| 54 | 短链接访问日志表 | 11 |  |
| 55 | 账户机构余额表 | 11 |  |
| 56 | 产品套餐关联表 | 11 |  |
| 57 | 发票信息表 | 11 |  |
| 58 | 邮件短信发送队列表 | 11 |  |
| 59 | 问卷分析表 | 11 |  |
| 60 | 问卷分析数据源表 | 11 |  |
| 61 | 画像规则表 | 11 |  |
| 62 | 短信邮件回调队列信息 | 11 |  |
| 63 | 机构主页编辑表 | 10 |  |
| 64 | 用户分组 | 10 |  |
| 65 | 分组同步表 | 10 |  |
| 66 | 维护计划任务 | 10 |  |
| 67 | 用户菜单关系表 | 10 |  |
| 68 | 问卷调查结果表 | 10 |  |
| 69 | 邮件信息统计表 | 10 |  |
| 70 | 地址表 | 10 |  |
| 71 | 机构月度信息统计表 | 10 |  |
| 72 | 公众号消息模板表 | 9 |  |
| 73 | 机构类型表 | 9 |  |
| 74 | 平台表 | 9 |  |
| 75 | 账户用户关系表 | 9 |  |
| 76 | 邮箱验证表 | 9 |  |
| 77 | 机构账户余额表 | 9 |  |
| 78 | 模块图片表 | 9 |  |
| 79 | 部门职位表 | 9 |  |
| 80 | 捐赠人用户轨迹表 | 9 |  |
| 81 | 短信信息统计表 | 9 |  |
| 82 | 微信消息回复表 | 9 |  |
| 83 | 协作表 | 9 |  |
| 84 | 签到设置表  | 9 |  |
| 85 | 静态文案链接访问日志表 | 9 |  |
| 86 | 微信公众账号表 | 8 |  |
| 87 | 公众号基本信息表 | 8 |  |
| 88 | 错误日志表 | 8 |  |
| 89 | 机构平台关系表 | 8 |  |
| 90 | 问卷文件表 | 8 |  |
| 91 | 短信发送记录表 | 8 |  |
| 92 | 短信扣费记录表 | 8 |  |
| 93 | 电话联络统计表  | 8 |  |
| 94 | 其他联络统计表   | 8 |  |
| 95 | 业务消息通知详细表 | 8 |  |
| 96 | 角色菜单关系表 | 8 |  |
| 97 | 用户角色关系表 | 8 |  |
| 98 | 网站授权表 | 8 |  |
| 99 | 短链接表 | 8 |  |
| 100 | 微信消息模板库 | 8 |  |
| 101 | 画像表 | 8 |  |
| 102 | 短信邮件回调队列信息临时 | 8 |  |
| 103 | 公众号授权信息表 | 7 |  |
| 104 | 公众号关联行业信息表 | 7 |  |
| 105 | 操作日志表 | 7 |  |
| 106 | 捐赠人维护记录表 | 7 |  |
| 107 | 邮件扣费记录表 | 7 |  |
| 108 | 联络用户组 | 7 |  |
| 109 | 账户钱包表 | 7 |  |
| 110 | 业务消息通知功能项表 | 6 |  |
| 111 | 传统节日表 | 6 |  |
| 112 | 邮件验证信息表 | 6 |  |
| 113 | 传播渠道关联表 | 6 |  |
| 114 | 公众用户列表显示顺序 | 6 |  |
| 115 | 月捐用户数变更记录表 | 6 |  |
| 116 | 机构登录月统计表 | 6 |  |
| 117 | 帮助文档目录表 | 6 |  |
| 118 | 微信第三方平台调用凭据表 | 5 |  |
| 119 | 基本配置表 | 5 |  |
| 120 | 文件表 | 5 |  |
| 121 | 分组存储过程信息 | 5 |  |
| 122 | 短信回执代码表 | 5 |  |
| 123 | 域名KEY表 | 5 |  |
| 124 | 问卷标签关联表 | 5 |  |
| 125 | 教程视频表 | 5 |  |
| 126 | 公众号行业信息表 | 4 |  |
| 127 | 公众号模板关联表 | 4 |  |
| 128 | 定时器控制表 | 4 |  |
| 129 | 系统共通词表 | 4 |  |
| 130 | 邮件回复地址 | 4 |  |
| 131 | 海报用户关联表 | 4 |  |
| 132 | 静态文案链接访问日志统计表 | 4 |  |
| 133 | 机构微信公众号关联表 | 3 |  |
| 134 | 微信公众账号授权表 | 3 |  |
| 135 | 公众号行业模板表 | 3 |  |
| 136 | 分组捐赠人关系表 | 3 |  |
| 137 | 联络任务分组关系表 | 3 |  |
| 138 | 业务消息通知功能项权限关系表 | 3 |  |
| 139 | 签到码数串表 | 3 |  |
| 140 | 捐赠人用户ID | 2 |  |
| 141 | 分组存储过程状态 | 2 |  |
| 142 | 机构充值记录表 | 2 |  |
| 143 | 问卷系统字段关联表 | 0 |  |

## 主要表结构详情

### 捐赠人表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | userName | varchar | 32 | 否 |  | 第三方用户名 |
| 3 | userSource | int | 2 | 否 |  | 用户来源(1:联劝网 2：微信 3：用户导入 4：用户录入 5：用户合并 6：问卷调查 ) |
| 4 | userGuid | bigint | 32 | 否 |  | 用户名（唯一标示，年+月+日+时+分+秒+4位userID） |
| 5 | name | varchar | 64 |  |  | 真实姓名 |
| 6 | namePinyin | varchar | 64 |  |  | 姓名拼音 |
| 7 | nationality | int | 1 |  |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 8 | identitycard | varchar | 32 |  |  | 证件号（0：身份证号；1：护照；2：台胞证） |
| 9 | sex | int | 1 |  |  | 性别(0:未知;1:男;2:女) |
| 10 | birthday | Date | 0 |  |  | 出生日期（YYYY/MM/DD） |
| 11 | constellation | int | 2 |  |  | 星座（1至12，具体参见如下） |
| 12 | zodiac | int | 2 |  |  | 生肖（1至12，具体参见如下） |
| 13 | degree | int | 1 |  |  | 教育程度（0：未知；1：小学；2：初中；3：高中/中专；4大专：；5：本科；6：硕士；7：博士；8：博士后；9：文盲） |
| 14 | job | int | 1 |  |  | 职业信息（0：学生；1：公司职员；2：国企事业单位职员；3：自由职业者；4：公益从业人员；5：政府机关人员；6：媒体；7：其他） |
| 15 | ismarry | int | 1 |  |  | 婚姻状况(0:未知; 1:已婚; 2:未婚; 3:离异; 4:丧偶) |
| 16 | province | varchar | 32 |  |  | 现住址地址的省或直辖市的地名 |
| 17 | city | varchar | 32 |  |  | 现住址地址的城市 |
| 18 | area | varchar | 32 |  |  | 现住址地址的区 |
| 19 | fullAddress | varchar | 255 |  |  | 现住址具体地址 |
| 20 | telphone | varchar | 32 |  |  | 住宅电话 |
| 21 | phone | varchar | 32 |  |  | 移动手机 |
| 22 | mail | varchar | 64 |  |  | 邮箱 |
| 23 | qq | varchar | 64 |  |  | QQ号（个人信息） |
| 24 | qqCode | varchar | 16 |  |  | QQ（openid，认证登录用） |
| 25 | weibo | varchar | 64 |  |  | 微博（个人信息） |
| 26 | weiboCode | varchar | 48 |  |  | 微博（openid，认证登录用） |
| 27 | weixin | varchar | 64 |  |  | 微信（个人信息） |
| 28 | weixinCode | varchar | 32 |  |  | 微信（openid，认证登录用） |
| 29 | unionid | varchar | 32 |  |  | 微信用UNIONID |
| 30 | douyin | varchar | 64 |  |  | 抖音号 |
| 31 | linkedin | varchar | 64 |  |  | 领英号 |
| 32 | hukouProvince | varchar | 32 |  |  | 户籍地址的省或直辖市的地名 |
| 33 | hukouCity | varchar | 32 |  |  | 户籍址地址的城市 |
| 34 | hukouArea | varchar | 32 |  |  | 户籍址地址的区 |
| 35 | isCityWide | int | 1 |  |  | 现住址和户籍是同一个城市(0:未知; 1:不是; 2:是) |
| 36 | postCode | varchar | 10 |  |  | 邮政编码 |
| 37 | createTime | DateTime | 0 | 否 |  | 创建时间 |
| 38 | creator | varchar | 32 |  |  | 创建者用户名（系统账户时为空，其余不为空） |
| 39 | creatorAccount | varchar | 32 | 否 |  | 创建者账户名 |
| 40 | updateTime | DateTime | 0 |  |  | 更新时间 |
| 41 | updater | varchar | 32 |  |  | 更新者用户名（系统账户时为空，其余不为空） |
| 42 | updaterAccount | varchar | 32 |  |  | 更新者账户名 |
| 43 | fax | varchar | 32 |  |  | 传真 |
| 44 | company | varchar | 32 |  |  | 单位 |
| 45 | companyPost | varchar | 32 |  |  | 单位职务 |
| 46 | companyPhone | varchar | 32 |  |  | 单位电话 |
| 47 | taobaoAccount | varchar | 32 |  |  | 淘宝账号 |
| 48 | alipayAccount | varchar | 32 |  |  | 支付宝账号 |
| 49 | companyPhone | varchar | 32 |  |  | 单位部门 |
| 50 | nickName | varchar | 32 |  |  | 昵称 |
| 51 | remarks | varchar | 32 |  |  | 备注 |
| 52 | institutionId | int | 11 |  |  | 机构Id |
| 53 | flg | int | 1 |  |  | 回滚标志（0：不能回滚；1：能回滚） |
| 54 | userSourceCode | varchar | 50 |  |  | 公众号原始id |
| 55 | headimgurl | varchar | 200 |  |  | 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。 |
| 56 | ip | varchar | 64 |  |  | ip地址 |
| 57 | shareAccount | varchar | 1024 |  |  | 数据共享给子账户的名单 |

### 捐赠人重复表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | userName | varchar | 32 | 否 |  | 第三方用户名 |
| 3 | userSource | int | 2 | 否 |  | 用户来源(1:联劝网 2：微信 3：用户导入 4：用户录入 5：用户合并 ) |
| 4 | userGuid | bigint | 32 | 否 |  | 用户名（唯一标示，年+月+日+时+分+秒+4位userID） |
| 5 | name | varchar | 64 |  |  | 真实姓名 |
| 6 | namePinyin | varchar | 64 |  |  | 姓名拼音 |
| 7 | nationality | int | 1 |  |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 8 | identitycard | varchar | 32 |  |  | 证件号（0：身份证号；1：护照；2：台胞证） |
| 9 | sex | int | 1 |  |  | 性别(0:未知;1:男;2:女) |
| 10 | birthday | Date | 0 |  |  | 出生日期（YYYY/MM/DD） |
| 11 | constellation | int | 2 |  |  | 星座（1至12，具体参见如下） |
| 12 | zodiac | int | 2 |  |  | 生肖（1至12，具体参见如下） |
| 13 | degree | int | 1 |  |  | 教育程度（0：未知；1：小学；2：初中；3：高中/中专；4大专：；5：本科；6：硕士；7：博士；8：博士后；9：文盲） |
| 14 | job | int | 1 |  |  | 职业信息（0：学生；1：公司职员；2：国企事业单位职员；3：自由职业者；4：公益从业人员；5：政府机关人员；6：媒体；7：其他） |
| 15 | ismarry | int | 1 |  |  | 婚姻状况(0:未知; 1:已婚; 2:未婚; 3:离异; 4:丧偶) |
| 16 | province | varchar | 32 |  |  | 现住址地址的省或直辖市的地名 |
| 17 | city | varchar | 32 |  |  | 现住址地址的城市 |
| 18 | area | varchar | 32 |  |  | 现住址地址的区 |
| 19 | fullAddress | varchar | 255 |  |  | 现住址具体地址 |
| 20 | telphone | varchar | 32 |  |  | 住宅电话 |
| 21 | phone | varchar | 32 |  |  | 移动手机 |
| 22 | mail | varchar | 64 |  |  | 邮箱 |
| 23 | qq | varchar | 64 |  |  | QQ号（个人信息） |
| 24 | qqCode | varchar | 16 |  |  | QQ（openid，认证登录用） |
| 25 | weibo | varchar | 64 |  |  | 微博（个人信息） |
| 26 | weiboCode | varchar | 48 |  |  | 微博（openid，认证登录用） |
| 27 | weixin | varchar | 64 |  |  | 微信（个人信息） |
| 28 | weixinCode | varchar | 32 |  |  | 微信（openid，认证登录用） |
| 29 | unionid | varchar | 32 |  |  | 微信用UNIONID |
| 30 | hukouProvince | varchar | 32 |  |  | 户籍地址的省或直辖市的地名 |
| 31 | hukouCity | varchar | 32 |  |  | 户籍址地址的城市 |
| 32 | hukouArea | varchar | 32 |  |  | 户籍址地址的区 |
| 33 | isCityWide | int | 1 |  |  | 现住址和户籍是同一个城市(0:未知; 1:不是; 2:是) |
| 34 | postCode | varchar | 10 |  |  | 邮政编码 |
| 35 | createTime | DateTime | 0 | 否 |  | 创建时间 |
| 36 | creator | varchar | 32 |  |  | 创建者用户名（系统账户时为空，其余不为空） |
| 37 | creatorAccount | varchar | 32 | 否 |  | 创建者账户名 |
| 38 | updateTime | DateTime | 0 |  |  | 更新时间 |
| 39 | updater | varchar | 32 |  |  | 更新者用户名（系统账户时为空，其余不为空） |
| 40 | updaterAccount | varchar | 32 |  |  | 更新者账户名 |
| 41 | fax | varchar | 32 |  |  | 传真 |
| 42 | company | varchar | 32 |  |  | 单位 |
| 43 | companyPost | varchar | 32 |  |  | 单位职务 |
| 44 | companyPhone | varchar | 32 |  |  | 单位电话 |
| 45 | taobaoAccount | varchar | 32 |  |  | 淘宝账号 |
| 46 | alipayAccount | varchar | 32 |  |  | 支付宝账号 |
| 47 | companyPhone | varchar | 32 |  |  | 单位部门 |
| 48 | nickName | varchar | 32 |  |  | 昵称 |
| 49 | remarks | varchar | 32 |  |  | 备注 |
| 50 | institutionId | int | 11 |  |  | 机构Id |
| 51 | flg | int | 1 |  |  | 回滚标志（0：不能回滚；1：能回滚） |
| 52 | userSourceCode | varchar | 50 |  |  | 公众号原始id |
| 53 | headimgurl | varchar | 200 |  |  | 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。 |
| 54 | douyin | varchar | 64 |  |  | 抖音号 |
| 55 | linkedin | varchar | 64 |  |  | 领英号 |
| 56 | ip | varchar | 64 |  |  | ip地址 |
| 57 | newUserGuid | bigint | 32 | 否 |  | 合并后新的Guid |

### 捐赠人临时表 

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | userName | varchar | 32 | 否 |  | 第三方用户名 |
| 3 | userGuid | bigint | 32 | 否 |  | 用户名（唯一标示，M+年+月+日+时+分+秒+） |
| 4 | name | varchar | 64 |  |  | 真实姓名 |
| 5 | namePinyin | varchar | 64 |  |  | 姓名拼音 |
| 6 | nationality | int | 1 |  |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 7 | identitycard | varchar | 32 |  |  | 证件号（0：身份证号；1：护照；2：台胞证） |
| 8 | sex | int | 1 |  |  | 性别(0:未知;1:男;2:女) |
| 9 | birthday | Date | 0 |  |  | 出生日期（YYYY/MM/DD） |
| 10 | constellation | int | 2 |  |  | 星座（1至12，具体参见如下） |
| 11 | zodiac | int | 2 |  |  | 生肖（1至12，具体参见如下） |
| 12 | degree | int | 1 |  |  | 教育程度（0：未知；1：小学；2：初中；3：高中/中专；4大专：；5：本科；6：本科以上；） |
| 13 | job | int | 1 |  |  | 职业信息（0：学生；1：公司职员；2：国企事业单位职员；3：自由职业者；4：公益从业人员；5：政府机关人员；6：媒体；7：其他） |
| 14 | ismarry | int | 1 |  |  | 婚姻状况(0:未知; 1:已婚; 2:未婚; 3:离异; 4:丧偶) |
| 15 | province | varchar | 32 |  |  | 现住址地址的省或直辖市的地名 |
| 16 | city | varchar | 32 |  |  | 现住址地址的城市 |
| 17 | area | varchar | 32 |  |  | 现住址地址的区 |
| 18 | fullAddress | varchar | 255 |  |  | 现住址具体地址 |
| 19 | telphone | varchar | 32 |  |  | 住宅电话 |
| 20 | phone | varchar | 32 |  |  | 移动手机 |
| 21 | mail | varchar | 64 |  |  | 邮箱 |
| 22 | qq | varchar | 64 |  |  | QQ（openid，认证登录用） |
| 23 | qqCode | varchar | 16 |  |  | QQ号（个人信息） |
| 24 | weibo | varchar | 64 |  |  | 微博（openid，认证登录用） |
| 25 | weiboCode | varchar | 48 |  |  | 微博（个人信息） |
| 26 | weixin | varchar | 64 |  |  | 微信（openid，认证登录用） |
| 27 | weixinCode | varchar | 32 |  |  | 微信（个人信息） |
| 28 | unionid | varchar | 32 |  |  | 微信用UNIONID |
| 29 | hukouProvince | varchar | 32 |  |  | 户籍地址的省或直辖市的地名 |
| 30 | hukouCity | varchar | 32 |  |  | 户籍址地址的城市 |
| 31 | hukouArea | varchar | 32 |  |  | 户籍址地址的区 |
| 32 | isCityWide | int | 1 |  |  | 现住址和户籍是同一个城市(0:未知; 1:不是; 2:是) |
| 33 | postCode | varchar | 10 |  |  | 邮政编码 |
| 34 | createTime | DateTime | 0 | 否 |  | 创建时间 |
| 35 | creator | varchar | 32 |  |  | 创建者用户名（系统账户时为空，其余不为空） |
| 36 | creatorAccount | varchar | 32 | 否 |  | 创建者账户名 |
| 37 | updateTime | DateTime | 0 |  |  | 更新时间 |
| 38 | updater | varchar | 32 |  |  | 更新者用户名（系统账户时为空，其余不为空） |
| 39 | updaterAccount | varchar | 32 |  |  | 更新者账户名 |
| 40 | fax | varchar | 32 |  |  | 传真 |
| 41 | company | varchar | 32 |  |  | 单位 |
| 42 | companyPost | varchar | 32 |  |  | 单位职务 |
| 43 | companyPhone | varchar | 32 |  |  | 单位电话 |
| 44 | taobaoAccount | varchar | 32 |  |  | 淘宝账号 |
| 45 | alipayAccount | varchar | 32 |  |  | 支付宝账号 |
| 46 | companyPhone | varchar | 32 |  |  | 单位部门 |
| 47 | nickName | varchar | 32 |  |  | 昵称 |
| 48 | remarks | varchar | 32 |  |  | 备注 |
| 49 | institutionId | int | 11 |  |  | 机构Id |
| 50 | flg | int | 1 |  |  | 标志 |
| 51 | successFlag | int | 1 |  |  | 导入是否成功（0：成功；1：失败；2重复） |
| 52 | errorMsg | varchar | 255 |  |  | 错误原因 |
| 53 | excelNum | int | 4 |  |  | 每行数据所在excel表的列 |
| 54 | douyin | varchar | 64 |  |  | 抖音号 |
| 55 | linkedin | varchar | 64 |  |  | 领英号 |
| 56 | ip | varchar | 64 |  |  | ip地址 |

### 机构表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | institutionEntityGuid | varchar | 32 | 否 |  | 机构实体ID（建资金账户用） |
| 3 | institutionName | varchar | 64 | 否 |  | 机构名称(唯一性) |
| 4 | threeInOneCertificate | int | 11 |  |  | 三合一营业执照（保存数据：文件ID；图片） |
| 5 | institutionCode | varchar | 64 |  |  | 统一社会信用代码 |
| 6 | account | varchar | 32 |  |  | 账号（系统中的资金账户，认证通过时开通资金账户） |
| 7 | status | int | 1 | 否 | 0 | 状态(0：未认证；1：认证审核中；2：已认证；3：已注销；4：已过期；5：认证不通过；6：已确认) |
| 8 | createTime | DateTime | 0 |  |  | 创建时间 |
| 9 | creater | DateTime | 0 |  |  | 创建者用户名 |
| 10 | submissionTime | DateTime | 0 |  |  | 认证提交时间（最新） |
| 11 | authTime | DateTime | 0 |  |  | 认证通过时间（最新） |
| 12 | auther | varchar | 255 |  |  | 认证者用户名 |
| 13 | reason | varchar | 255 |  |  | 认证不通过理由 |
| 14 | userId | int | 11 | 否 |  | 用户表ID(机构管理员) |
| 15 | packageGuid | varchar | 32 |  |  | 当前购买的套餐 |
| 16 | limitTime | DateTime |  |  |  | 过期时间 |
| 17 | isLianQuan | int |  |  | 0 | 是否是联劝网用户（0：非联劝网用户；1：是联劝网用户） |
| 18 | institutionType | int | 2 | 否 |  | 机构类型(1：非公募基金；2：公募基金；) |
| 19 | publicAptitudeCertificate | int | 11 |  |  | 公募资质证明（保存数据：文件ID；图片） |
| 20 | SynBtnClickTime | DateTime |  |  |  | 最近一次点击同步scrm数据按钮的时间 |
| 21 | subjectPicId | int | 11 |  |  | 主题图片ID，用于列表（主题图片比例为8：9） |
| 22 | subjectBigPicId | int | 11 |  |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 23 | showDetail | int | 1 |  | 0 | 是否在联劝网上显示0：草稿（下线）；1：线上 |
| 24 | isPartner | int | 1 |  | 0 | 是否是联盟伙伴0：不是；1：是 |
| 25 | information | varchar | 300 |  |  | 资讯详情 |
| 26 | partnerStatus | int | 1 |  | 0 | 机构主页状态（0：草稿；1：审核中；2：审核通过；3：审核不通过） |
| 27 | applyTime | DateTime | 0 |  |  | 机构主页提交审核申请时间 |
| 28 | reviewTime | DateTime | 0 |  |  | 机构主页审核时间 |
| 29 | partnerReason | varchar | 255 |  |  | 机构主页审核不通过理由 |
| 30 | cancelTime | datetime |  |  |  | 注销时间 |
| 31 | isCharity | int |  |  |  | 是否为慈善组织（0：否；1：是；） |
| 32 | insType | int |  |  |  | 机构类型（详见《机构类型表》） |
| 33 | certificateStartTime | DateTime |  |  |  | 三合一证书有效期（开始日期） |
| 34 | certificateEndTime | DateTime |  |  |  | 三合一证书有效期（结束日期） |
| 35 | publicCertificateStartTime | DateTime |  |  |  | 资质证明有效期（开始日期） |
| 36 | publicCertificateEndTime | DateTime |  |  |  | 资质证明有效期（结束日期） |
| 37 | foundTime | varchar | 4 |  |  | 成立时间 |
| 38 | realm | varchar | 100 |  |  | 机构领域 |
| 39 | scale | varchar | 100 |  |  | 机构规模 |
| 40 | registerPlace | varchar | 100 |  |  | 登记机关 |
| 41 | officialUrl | varchar | 255 |  |  | 官方网址 |
| 42 | wechatAccount | varchar | 255 |  |  | 微信公众号 |
| 43 | introduction | varchar | 300 |  |  | 机构简介 |
| 44 | disclosure | varchar | 255 |  |  | 信息公开 |
| 45 | honor | varchar | 100 |  |  | 组织荣誉 |
| 46 | partnerInfoStatus | int |  |  |  | 机构信息展示审核状态（0：草稿；1：审核中；2：审核通过；3：审核不通过） |
| 47 | partnerInfoReason | varchar | 255 |  |  | 机构信息展示审核不通过理由 |
| 48 | partnerInfoApplyTime | datetime |  |  |  | 机构信息展示提交审核申请时间 |
| 49 | partnerInfoReviewTime | datetime |  |  |  | 机构信息展示审核时间 |

### 机构信息修改表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | institutionEntityGuid | varchar | 32 | 否 |  | 机构实体ID（建资金账户用） |
| 3 | institutionName | varchar | 64 | 否 |  | 机构名称(唯一性) |
| 4 | threeInOneCertificate | int | 11 |  |  | 三合一营业执照（保存数据：文件ID；图片） |
| 5 | institutionCode | varchar | 64 |  |  | 统一社会信用代码 |
| 6 | account | varchar | 32 |  |  | 账号（系统中的资金账户，认证通过时开通资金账户） |
| 7 | status | int | 1 | 否 | 0 | 状态(0：未认证；1：认证审核中；2：已认证；3：已注销；4：已过期；5：认证不通过；6：已确认) |
| 8 | createTime | DateTime | 0 |  |  | 创建时间 |
| 9 | creater | DateTime | 0 |  |  | 创建者用户名 |
| 10 | submissionTime | DateTime | 0 |  |  | 认证提交时间（最新） |
| 11 | authTime | DateTime | 0 |  |  | 认证通过时间（最新） |
| 12 | auther | varchar | 255 |  |  | 认证者用户名 |
| 13 | reason | varchar | 255 |  |  | 认证不通过理由 |
| 14 | userId | int | 11 | 否 |  | 用户表ID(机构管理员) |
| 15 | packageGuid | varchar | 32 |  |  | 当前购买的套餐 |
| 16 | limitTime | DateTime |  |  |  | 过期时间 |
| 17 | isLianQuan | int |  |  | 0 | 是否是联劝网用户（0：非联劝网用户；1：是联劝网用户） |
| 18 | institutionType | int | 2 | 否 |  | 机构类型(1：非公募基金；2：公募基金；) |
| 19 | publicAptitudeCertificate | int | 11 |  |  | 公募资质证明（保存数据：文件ID；图片） |
| 20 | SynBtnClickTime | DateTime |  |  |  | 最近一次点击同步scrm数据按钮的时间 |
| 21 | subjectPicId | int | 11 |  |  | 主题图片ID，用于列表（主题图片比例为8：9） |
| 22 | subjectBigPicId | int | 11 |  |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 23 | showDetail | int | 1 |  | 0 | 是否在联劝网上显示0：草稿（下线）；1：线上 |
| 24 | isPartner | int | 1 |  | 0 | 是否是联盟伙伴0：不是；1：是 |
| 25 | information | varchar | 300 |  |  | 资讯详情 |
| 26 | partnerStatus | int | 1 |  | 0 | 机构主页状态（0：草稿；1：审核中；2：审核通过；3：审核不通过） |
| 27 | applyTime | DateTime | 0 |  |  | 机构主页提交审核申请时间 |
| 28 | reviewTime | DateTime | 0 |  |  | 机构主页审核时间 |
| 29 | partnerReason | varchar | 255 |  |  | 机构主页审核不通过理由 |
| 30 | cancelTime | datetime |  |  |  | 注销时间 |
| 31 | isCharity | int |  |  |  | 是否为慈善组织（0：否；1：是；） |
| 32 | insType | int |  |  |  | 机构类型（0：基金会；1：社会团体；2：社会服务机构；） |
| 33 | certificateStartTime | DateTime |  |  |  | 三合一证书有效期（开始日期） |
| 34 | certificateEndTime | DateTime |  |  |  | 三合一证书有效期（结束日期） |
| 35 | publicCertificateStartTime | DateTime |  |  |  | 资质证明有效期（开始日期） |
| 36 | publicCertificateEndTime | DateTime |  |  |  | 资质证明有效期（结束日期） |
| 37 | editApplyTime | DateTime |  |  |  | 机构信息修改提交审核时间 |

### 用户表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | userName | varchar | 32 | 否 |  | 用户名 |
| 3 | name | varchar | 64 |  |  | 真实姓名 |
| 4 | namePinyin | varchar | 64 |  |  | 姓名拼音 |
| 5 | sex | int | 1 |  | 0 | 性别(0:未知;1:男;2:女) |
| 6 | birthday | Date |  |  |  | 出生日期（YYYY/MM/DD） |
| 7 | phone | varchar | 64 |  |  | 手机 |
| 8 | mail | varchar | 64 |  |  | 邮箱 |
| 9 | nickname | varchar | 64 |  |  | 用户昵称 |
| 10 | institutionId | int | 11 | 否 |  | 组织机构id |
| 11 | deptId | int | 11 |  |  | 部门id |
| 12 | position | varchar | 32 |  |  | 职位 |
| 13 | qq | varchar | 64 |  |  | QQ（openid，认证登录用） |
| 14 | qqCode | varchar | 16 |  |  | QQ号（个人信息） |
| 15 | weibo | varchar | 64 |  |  | 微博（openid，认证登录用） |
| 16 | weiboCode | varchar | 48 |  |  | 微博（个人信息） |
| 17 | weixin | varchar | 64 |  |  | 微信（openid，认证登录用） |
| 18 | weixinCode | varchar | 32 |  |  | 微信（个人信息） |
| 19 | unionid | varchar | 32 |  |  | 微信用UNIONID |
| 20 | province | varchar | 16 |  |  | 省份 微信用 |
| 21 | city | varchar | 32 |  |  | 城市 微信用 |
| 22 | country | varchar | 16 |  |  | 国家 微信用 |
| 23 | headimgurl | varchar | 255 |  |  | 头像URL 微信用 |
| 24 | avatar | int | 11 |  |  | 头像id |
| 25 | createTime | DateTime | 0 | 否 |  | 创建时间 |
| 26 | creator | varchar | 32 |  |  | 创建者用户名（系统账户时为空，其余不为空） |
| 27 | creatorAccount | varchar | 32 | 否 |  | 创建者账户名 |
| 28 | updateTime | DateTime | 0 |  |  | 更新时间 |
| 29 | updater | varchar | 32 |  |  | 更新者用户名（系统账户时为空，其余不为空） |
| 30 | updaterAccount | varchar | 32 |  |  | 更新者账户名 |

### 机构新闻表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | institutionId | int | 11 | 否 |  | 所属机构id |
| 3 | title | varchar | 100 | 否 |  | 新闻主标题 |
| 4 | sourceid | int | 1 | 是 |  | 是否原创（0：原则；1：转载；2：单附件） |
| 5 | newssource | varcher | 32 | 是 |  | 新闻来源 |
| 6 | newsurl | varcher | 255 | 是 |  | 新闻来源链接 |
| 7 | summary | varcher | 255 | 是 |  | 新闻提要 |
| 8 | releaseTime | datetime |  | 是 |  | 新闻发布时间 |
| 9 | type | int | 1 | 否 |  | 所属类型（1：新闻； 2：他方链接…） |
| 10 | status | int | 1 | 否 |  | 新闻状态（0：草稿；1：初审中；2：初审未通过；3：最终审核中、初核通过；4：终审通过；5：终审未通过；6：删除；7：作废（审核通过后，再次更新内容时，当前条作废，重新生成一条）） |
| 11 | insertTime | datetime |  | 否 |  | 创建时间 |
| 12 | inserter | varchar | 32 | 否 |  | 创建者（用户名） |
| 13 | insertNickName | varchar | 32 | 否 |  | 创建者昵称 |
| 14 | applyTime | datetime |  | 是 |  | 申请时间 |
| 15 | reviewDetail | varchar | 200 | 是 |  | 初审不通过的理由 |
| 16 | reviewTime | datetime |  | 是 |  | 初审时间 |
| 17 | reviewer | varchar | 32 | 是 |  | 初审者（用户名） |
| 18 | reviewNickName | varchar | 32 | 是 |  | 初审者昵称 |
| 19 | reviewDetail2 | varchar | 200 | 是 |  | 终审不通过的理由 |
| 20 | reviewTime2 | datetime |  | 是 |  | 终审时间 |
| 21 | reviewer2 | varchar | 32 | 是 |  | 终审者（用户名） |
| 22 | reviewNickName2 | varchar | 32 | 是 |  | 终审者昵称 |
| 23 | mark | int | 1 | 是 |  | 标志（1：红；2：黄；3：绿；0：无） |
| 24 | keywords1 | varchar | 10 | 是 |  | 关键字1 |
| 25 | keywords2 | varchar | 10 | 是 |  | 关键字2 |
| 26 | keywords3 | varchar | 10 | 是 |  | 关键字3 |
| 27 | pic | longblob |  | 是 |  | 图片内容（字节流） |
| 28 | picExt | varchar | 10 | 是 |  | 图片类型 |
| 29 | picName | varchar | 255 | 是 |  | 图片名称 |

### 公众号用户消息表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | 自动编号,自动增长 |
| 2 | toUserId | bigint | 20 | 否 |  | 收到消息一方的用户id，关联微信个人用户表或微信公众账号基本信息表 |
| 3 | fromUserId | bigint | 20 | 否 |  | 消息发送方的用户id |
| 4 | toUserName | varchar | 50 | 否 |  | 接收方微信号 |
| 5 | fromUserName | varchar | 50 | 否 |  | 发送方微信号openid |
| 6 | createTime | timestamp |  | 否 |  | 消息创建时间 |
| 7 | msgType | tinyint | 4 | 否 |  | 消息类型 |
| 8 | msgId | bigint | 64 | 否 |  | 消息id，64位整型 |
| 9 | content | varchar | 500 | 是 |  | 消息内容 |
| 10 | picUrl | varchar | 500 | 是 |  | 图片链接 |
| 11 | mediaId | varchar | 200 | 是 |  | 图片消息媒体id，可以调用多媒体文件下载接口拉取数据。 |
| 12 | format | varchar | 20 | 是 |  | 语音格式，如amr，speex等 |
| 13 | recongnition | varchar | 50 | 是 |  | 语音识别结果 |
| 14 | thumbMediaId | varchar | 50 | 是 |  | 视频消息缩略图的媒体id，可以调用多媒体文件下载接口拉取数据 |
| 15 | locationX | varchar | 50 | 是 |  | 地理位置维度 |
| 16 | locationY | varchar | 50 | 是 |  | 地理位置经度 |
| 17 | scale | int | 11 | 是 |  | 地图缩放大小 |
| 18 | locationLabel | varchar | 50 | 是 |  | 地理位置信息 |
| 19 | title | varchar | 50 | 是 |  | 消息标题 |
| 20 | description | varchar | 500 | 是 |  | 消息描述 |
| 21 | url | varchar | 200 | 是 |  | 消息链接 |
| 22 | ticket | varchar | 50 | 是 |  | 二维码的ticket，可用来换取二维码图片 |
| 23 | musicUrl | varchar | 200 | 是 |  | 音乐链接 |
| 24 | hqMusicUrl | varchar | 200 | 是 |  | 高质量音乐链接，WIFI环境优先使用该链接播放音乐 |
| 25 | articleCount | int | 11 | 是 |  | 文章数量 |
| 26 | collection | int | 1 | 是 |  | 此条信息是否收藏（0：未收藏；1：已收藏 |
| 27 | reply | int | 1 | 是 |  | 是否回复（0：未回复；1：已回复） |

### 邮件发送队列表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | emailID | varchar | 32 |  |  | 邮件任务ID（生成规则：EMLYYYYMMDD24hmmsssss+3位数字随机数，退订/统计用） |
| 3 | senderName | varchar | 50 |  |  | 发送方 |
| 4 | senderEmail | varchar | 100 |  |  | 群发邮件地址 |
| 5 | replyToEmail | varchar | 100 |  |  | 回复邮件地址 |
| 6 | email | varchar | 200 |  |  | 收件方邮件地址 |
| 7 | mailTitle | varchar | 100 |  |  | 邮件标题（邮件发送时用） |
| 8 | mailContent | longtext |  |  |  | 邮件正文（邮件发送时用） |
| 9 | canUnsubscribe | tinyint | 1 |  | 0 | 是否可以退订（0：不可退订 1：可以退订）（邮件发送时用） |
| 10 | sendCount | int | 1 |  | 1 | 可重复发送次数 |
| 11 | lastRunTime | datetime |  |  |  | 最后一次发送时间 |
| 12 | important | int | 1 |  | 5 | 邮件重要度（由低到高，数值越大，代表重要度越高，优先发送，暂定1-5） |
| 13 | createTime | datetime |  |  |  | 生成时间 |
| 14 | sendStatus | int | 1 |  |  | 状态（0：待发送 1：发送成功 2：发送失败 3:发送中 ） |
| 15 | sendId | varchar | 32 |  |  | 发送id |
| 16 | errmsg | varchar | 500 |  |  | 失败信息 |
| 17 | userGuid | bigint | 32 |  |  | 捐赠人GUID |
| 18 | nameOrNickname | varchar | 128 |  |  | 姓名（昵称） |
| 19 | readFlag | int | 1 |  | 0 | 是否阅读（0：未读 1：已读） |
| 20 | mailUnsubscribeInfo | varchar | 500 |  |  | 邮件结尾退订内容 |
| 21 | emailIdEncode | varchar | 128 |  |  | 加密后的邮件ID |
| 22 | emailEncode | varchar | 128 |  |  | 加密后的邮箱地址 |
| 23 | unsubscribeClickFlg | int | 11 |  | 0 | 判断是否点击过退订按钮（0：否，1：是） |
| 24 | receiptTime | datetime |  |  |  | 邮件结果回复时间 |
| 25 | creater | varchar | 32 |  |  | 生成者 |
| 26 | attachment | varchar | 32 |  |  | 附件ids |

### 发票表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | invoiceTitle | varvhar | 255 |  |  | 发票抬头 |
| 3 | taxpayerCode | varchar | 32 |  |  | 纳税人识别号 |
| 4 | companyAddress | varchar | 255 |  |  | 公司地址 |
| 5 | companyPhone | varvhar | 32 |  |  | 公司电话 |
| 6 | openBank | varchar | 32 |  |  | 开户银行 |
| 7 | bankNo | varchar | 32 |  |  | 开户账号 |
| 8 | createTime | datetime | 0 |  |  | 创建时间 |
| 9 | creater | varchar | 32 |  |  | 创建者 |
| 10 | invoiceType | int | 11 |  |  | 发票类型（1：普通发票，2：专用发票） |
| 11 | receiveWay | int | 11 |  |  | 获取方式（0：邮寄，1：电子发票） |
| 12 | receiver | varchar | 32 |  |  | 收件人姓名 |
| 13 | province | varchar | 32 |  |  | 省份 |
| 14 | city | varchar | 32 |  |  | 城市 |
| 15 | area | varchar | 32 |  |  | 地区 |
| 16 | address | varchar | 255 |  |  | 地址 |
| 17 | phone | varchar | 32 |  |  | 手机号码 |
| 18 | remarks | varchar | 255 |  |  | 备注 |
| 19 | institutionEntityGuid | varchar | 32 |  |  | 机构GUID |
| 20 | invoiceStatus | int | 11 | 否 | 0 | 发票状态（0：正在处理，1：处理完成） |
| 21 | itemType | int | 11 |  |  | 发票对象（1：充值） |
| 22 | itemIds | text | 0 |  |  | 发票对象ID（可有多个） |
| 23 | totalAmount | double | 0 |  |  | 发票金额 |
| 24 | updateTime | datetime | 0 |  |  | 修改时间 |
| 25 | updater | varchar | 32 |  |  | 修改者 |
| 26 | email | varchar | 64 |  |  | 收件人邮箱 |

