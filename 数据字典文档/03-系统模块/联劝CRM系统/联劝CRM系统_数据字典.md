# 联劝CRM系统 数据字典

## 系统概述

**系统名称**: 联劝CRM系统  
**系统标识**: lqscrm  
**文件来源**: lqscrm_dataBaseModelDesign.xls  
**工作表总数**: 58  
**有效表数量**: 53  
**字段总数**: 580  

## 表结构清单

| 序号 | 表名 | 字段数 | 说明 |
|------|------|--------|------|
| 1 | 捐赠人表 | 35 |  |
| 2 | 公众号用户消息表 | 27 |  |
| 3 | 邮件发送队列表 | 26 |  |
| 4 | 发票表 | 23 |  |
| 5 | 微信推送接收人表  | 20 |  |
| 6 | 短信发送队列表 | 20 |  |
| 7 | 标签表 | 18 |  |
| 8 | 推广传播表 | 17 |  |
| 9 | 邮件任务 | 16 |  |
| 10 | 短信任务  | 16 |  |
| 11 | 公众号素材表 | 14 |  |
| 12 | 传播访问日志表 | 14 |  |
| 13 | 黑名单表 | 13 |  |
| 14 | 用户分组规则 | 13 |  |
| 15 | 微信第三方平台配置表 | 12 |  |
| 16 | 公众号模板消息发送记录表 | 12 |  |
| 17 | 邮件发送统计表 | 12 |  |
| 18 | 微信模板发送统计表 | 11 |  |
| 19 | 问卷交叉分析表 | 11 |  |
| 20 | 标签捐赠人关系表 | 11 |  |
| 21 | 联络任务 | 11 |  |
| 22 | 短信模板 | 11 |  |
| 23 | 联络记录表 | 11 |  |
| 24 | 渠道表 | 10 |  |
| 25 | 问卷自定义查询表 | 10 |  |
| 26 | 短信发送统计表 | 10 |  |
| 27 | 联络回调表 | 10 |  |
| 28 | 微信公众账号表 | 9 |  |
| 29 | 微信公众账号信息表  | 9 |  |
| 30 | 公众号消息模板表 | 9 |  |
| 31 | 微信消息回复表 | 9 |  |
| 32 | 传播渠道关联表 | 9 |  |
| 33 | 用户分组 | 9 |  |
| 34 | 公众号基本信息表 | 8 |  |
| 35 | 微信消息模板库 | 8 |  |
| 36 | 微信模板消息表 | 8 |  |
| 37 | 联系人备注 | 8 |  |
| 38 | 公众号授权信息表 | 7 |  |
| 39 | 公众号关联行业信息表 | 7 |  |
| 40 | 微信公众号关注用户情况表 | 7 |  |
| 41 | 发票订单表 | 7 |  |
| 42 | 发票标签处理表 | 7 |  |
| 43 | 微信公众账号口令表 | 6 |  |
| 44 | 微信标签表 | 6 |  |
| 45 | 微信第三方平台调用凭据表 | 5 |  |
| 46 | 公众号行业信息表 | 5 |  |
| 47 | 微信接收人标签表  | 4 |  |
| 48 | 公众号模板关联表 | 4 |  |
| 49 | 微信公众账号授权表 | 3 |  |
| 50 | 公众号行业模板表 | 3 |  |
| 51 | 分组捐赠人关系表 | 3 |  |
| 52 | 联络任务选中分组表 | 3 |  |
| 53 | 联络任务收件人表 | 3 |  |

## 主要表结构详情

### 捐赠人表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  |  |
| 2 | user_guid | varchar | 64 |  |  | 用户GUID |
| 3 | lqw_username | varchar | 32 |  |  | 联劝网用户名 |
| 4 | lqw_registration_time | datetime |  |  |  | 联劝网注册时间 |
| 5 | user_source | int | 11 |  |  | 用户来源(1:联劝网 2：用户添加) |
| 6 | nick_name | varchar | 32 |  |  | 匹配字段-昵称 |
| 7 | name | varchar | 64 |  |  | 匹配字段-姓名 |
| 8 | name_pinyin | varchar | 64 |  |  | 姓名拼音 |
| 9 | nationality | int | 11 |  |  | 匹配字段-国籍 |
| 10 | identitycard | varchar | 32 |  |  | 匹配字段-证件号 |
| 11 | gender | varchar | 2 |  |  | 匹配字段-性别 |
| 12 | birthday | date |  |  |  | 匹配字段-出生日期 |
| 13 | phone | varchar | 32 |  |  | 匹配字段-手机号 |
| 14 | donate_amount | decimal | 15,2 |  |  | 捐赠金额 |
| 15 | donate_count | int | 11 |  |  | 捐赠次数 |
| 16 | fundraise_amount | decimal | 15,2 |  |  | 筹款金额 |
| 17 | fundraise_count | int | 11 |  |  | 筹款次数 |
| 18 | sms_unsubscribe | tinyint | 4 |  | 0 | 短信退订(0:未退订 1：已退订) |
| 19 | mail | varchar | 64 |  |  | 匹配字段-邮箱 |
| 20 | mail_unsubscribe | tinyint | 4 |  | 0 | 邮件退订(0:未退订 1：已退订) |
| 21 | region | varchar | 255 |  |  | 匹配字段-地区 |
| 22 | wx_flag | tinyint | 4 |  | 0 | 是否关注微信（0：否 1：是） |
| 23 | wx_name | varchar | 1024 |  |  | 微信名称 |
| 24 | wx_unionid | varchar | 32 |  |  | 微信unionid |
| 25 | wx_openid | varchar | 40 |  |  | 微信用户的唯一标识 |
| 26 | mail_address | varchar | 255 |  |  | 邮件地址 |
| 27 | monthly_donation | tinyint | 4 |  |  | 月捐状态（0：否 1：是） |
| 28 | monthly_donation_no | varchar | 32 |  |  | 月捐编号 |
| 29 | first_donation_time | datetime |  |  |  | 首次捐赠时间 |
| 30 | last_donation_time | datetime |  |  |  | 最近捐赠时间 |
| 31 | import_guid | varchar | 40 |  |  | 导入批次 |
| 32 | create_time | datetime |  |  |  |  |
| 33 | creator | varchar | 32 |  |  |  |
| 34 | update_time | datetime |  |  |  |  |
| 35 | updater | varchar | 32 |  |  |  |

### 公众号用户消息表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | 自动编号,自动增长 |
| 2 | to_user_id | bigint | 20 | 否 |  | 收到消息一方的用户id，关联微信个人用户表或微信公众账号基本信息表 |
| 3 | from_user_id | bigint | 20 | 否 |  | 消息发送方的用户id |
| 4 | to_user_name | varchar | 50 | 否 |  | 接收方微信号 |
| 5 | from_user_name | varchar | 50 | 否 |  | 发送方微信号openid |
| 6 | create_time | timestamp |  | 否 |  | 消息创建时间 |
| 7 | msg_type | tinyint | 4 | 否 |  | 消息类型 |
| 8 | msg_id | bigint | 64 | 否 |  | 消息id，64位整型 |
| 9 | content | varchar | 500 | 是 |  | 消息内容 |
| 10 | pic_url | varchar | 500 | 是 |  | 图片链接 |
| 11 | media_id | varchar | 200 | 是 |  | 图片消息媒体id，可以调用多媒体文件下载接口拉取数据。 |
| 12 | format | varchar | 20 | 是 |  | 语音格式，如amr，speex等 |
| 13 | recongnition | varchar | 50 | 是 |  | 语音识别结果 |
| 14 | thumb_media_id | varchar | 50 | 是 |  | 视频消息缩略图的媒体id，可以调用多媒体文件下载接口拉取数据 |
| 15 | location_x | varchar | 50 | 是 |  | 地理位置维度 |
| 16 | location_y | varchar | 50 | 是 |  | 地理位置经度 |
| 17 | scale | int | 11 | 是 |  | 地图缩放大小 |
| 18 | location_label | varchar | 50 | 是 |  | 地理位置信息 |
| 19 | title | varchar | 50 | 是 |  | 消息标题 |
| 20 | description | varchar | 500 | 是 |  | 消息描述 |
| 21 | url | varchar | 200 | 是 |  | 消息链接 |
| 22 | ticket | varchar | 50 | 是 |  | 二维码的ticket，可用来换取二维码图片 |
| 23 | music_url | varchar | 200 | 是 |  | 音乐链接 |
| 24 | hq_music_url | varchar | 200 | 是 |  | 高质量音乐链接，WIFI环境优先使用该链接播放音乐 |
| 25 | article_count | int | 11 | 是 |  | 文章数量 |
| 26 | collection | int | 1 | 是 |  | 此条信息是否收藏（0：未收藏；1：已收藏 |
| 27 | reply | int | 1 | 是 |  | 是否回复（0：未回复；1：已回复） |

### 邮件发送队列表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | email_id | varchar | 32 |  |  | 邮件任务ID（生成规则：EMLYYYYMMDD24hmmsssss+3位数字随机数，退订/统计用） |
| 3 | sender_name | varchar | 50 |  |  | 发送方 |
| 4 | sender_email | varchar | 1024 |  |  | 群发邮件地址（不用回复） |
| 5 | reply_to_email | varchar | 1024 |  |  | 回复邮件地址 |
| 6 | email | varchar | 1024 |  |  | 收件方邮件地址 |
| 7 | mail_title | varchar | 1024 |  |  | 邮件标题 |
| 8 | mail_content | longtext |  |  |  | 邮件内容 |
| 9 | can_unsubscribe | int | 2 |  | 0 | 是否可以退订（0：不可退订 1：可以退订）（邮件发送时用） |
| 10 | send_count | int | 2 |  | 1 | 可重复发送次数 |
| 11 | last_run_time | datetime |  |  |  | 最后一次发送时间 |
| 12 | important | int | 2 |  | 1 | 邮件重要度（由低到高，数值越大，代表重要度越高，优先发送，暂定1-5） |
| 13 | create_time | datetime |  |  |  | 最后一次发送时间 |
| 14 | send_status | int | 2 |  |  | 状态（0：待发送 1：发送成功 2：发送失败 ） |
| 15 | send_id | varchar | 32 |  |  | 发送id |
| 16 | errmsg | varchar | 500 |  |  | 错误信息 |
| 17 | user_id | int | 11 |  |  | 用户id |
| 18 | user_guid | varchar | 32 |  |  | 用户guid |
| 19 | name_or_nickname | varchar | 255 |  | 0 | 姓名 |
| 20 | read_flag | int | 11 |  |  | 是否阅读（0：未读 1：已读） |
| 21 | mail_unsubscribe_info | mediumtext |  |  |  | 退订内容 |
| 22 | email_id_encode | varchar | 128 |  |  | 加密后的emailID |
| 23 | email_encode | varchar | 128 |  | 0 | 加密后的email |
| 24 | unsubscribe_click_flg | int | 11 |  |  | 判断是否点击过退订按钮（0：否，1：是） |
| 25 | receipt_time | datetime |  |  |  | 邮件结果回复时间 |
| 26 | creater | varchar | 255 |  |  | 创建者 |

### 发票表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID |
| 2 | entity_id | varchar | 64 |  |  | 用户id |
| 3 | user_guid | varchar | 64 |  |  | 用户guid |
| 4 | invoice_code | varchar | 16 |  |  | 发票编号（实际发票号码） |
| 5 | status | int | 11 |  |  | 发票状态（0：待开 1：正常（已开具） 2：作废） |
| 6 | print_time | datetime |  |  |  | 发票打印时间 |
| 7 | invoice_title | varchar | 128 |  |  | 发票抬头 |
| 8 | invoice_amount | decimal | 15，2 |  |  | 发票金额 |
| 9 | remark | varchar | 256 |  |  | 备注 |
| 10 | apply_time | datetime |  |  |  | 发票申请时间 |
| 11 | update_time | datetime |  |  |  | 发票申请信息修改时间 |
| 12 | invoice_time | datetime |  |  |  |  |
| 13 | apply_code | varchar | 32 |  |  | 申请编号 |
| 14 | serial_number | varchar | 64 |  |  | 发票系统编号 |
| 15 | invoice_img | varchar | 500 |  |  | 发票图片 |
| 16 | invoice_pdf | varchar | 500 |  |  | 发票pdf |
| 17 | short_url | varchar | 500 |  |  | 短链接地址 |
| 18 | invoice_type | int | 2 |  |  | 票据类型（0：匿名；1：个人；2：企业；） |
| 19 | card_type | int | 2 |  |  | 证件号类型（1：居民身份证；2：护照；） |
| 20 | credit_code | varchar | 64 |  |  | 证件号 |
| 21 | transform_pdf | varchar | 64 |  |  |  |
| 22 | paper_flag | int | 2 |  |  | 1:纸质 2：电子 |
| 23 | invalid_time | datetime |  |  |  | 作废时间 |

### 微信推送接收人表 

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | 自动编号,自动增长 |
| 2 | official_account_id | bigint | 20 | 是 |  | 用户所属的公众号id，关联公众号基本信息表 |
| 3 | openid | varchar | 50 | 是 |  | 用户的标识，对当前公众号唯一 |
| 4 | subscribe | tinyint | 4 | 是 |  | 用户是否订阅该公众号标识，值为0时，代表此用户没有关注该公众号，拉取不到其余信息，只有openid和UnionID（在该公众号绑定到了微信开放平台账号时才有）。 |
| 5 | nick_name | varchar | 1024 | 是 |  |  |
| 6 | sex | tinyint | 4 | 是 |  | 用户的性别，值为1时是男性，值为2时是女性，值为0时是未知 |
| 7 | city | varchar | 255 | 是 |  | 用户所在城市 |
| 8 | country | varchar | 255 | 是 |  | 用户所在国家 |
| 9 | province | varchar | 255 | 是 |  | 用户所在省份 |
| 10 | language | varchar | 20 | 是 |  | 用户的语言，简体中文为zh_CN |
| 11 | headimgurl | varchar | 1024 | 是 |  | 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。 |
| 12 | subscribe_time | timestamp |  | 是 |  | 用户关注时间，为时间戳。如果用户曾多次关注，则取最后关注时间 |
| 13 | unionid | varchar | 50 | 是 |  | 只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段。 |
| 14 | remark | varchar | 1024 | 是 |  | 公众号运营者对粉丝的备注，公众号运营者可在微信公众平台用户管理界面对粉丝添加备注 |
| 15 | status | tinyint | 4 | 是 |  | 用户状态 1，正常状态，2，黑名单，3，已经删除 |
| 16 | groupid | varchar | 50 | 是 |  | 用户所在的分组ID |
| 17 | create_time | timestamp |  | 是 |  | 消息创建时间 |
| 18 | is_bind | tinyint | 4 | 是 |  |  |
| 19 | cancel_time | timestamp |  | 是 |  | 用户取消关注时间 |
| 20 | show_nick_name | varchar | 1024 | 是 |  |  |

### 短信发送队列表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | sms_id | varchar | 32 |  |  | 短信任务ID（生成规则：SMSYYYYMMDD24hmmsssss+3位数字随机数，退订/统计用） |
| 3 | phone | varchar | 11 |  |  | 手机号 |
| 4 | sms_code | varchar | 20 |  |  | 模板编号 |
| 5 | send_status | int | 1 |  | 0 | 状态（0：待发送 1：发送成功 2：发送失败 ） |
| 6 | content | text | 500 |  |  | 短信内容 |
| 7 | var_content | text | 500 |  |  | 短信变量（json格式） |
| 8 | errmsg | varchar | 500 |  |  | 错误信息 |
| 9 | create_time | datetime |  |  |  | 最后一次发送时间 |
| 10 | send_count | int | 1 |  | 1 | 可重复发送次数 |
| 11 | last_run_time | datetime |  |  |  | 最后一次发送时间 |
| 12 | important | int | 1 |  | 1 | 邮件重要度（由低到高，数值越大，代表重要度越高，优先发送，暂定1-5） |
| 13 | send_id | varchar | 32 |  |  | 发送id |
| 14 | user_id | int | 128 |  |  | 用户id |
| 15 | user_guid | varchar | 32 |  |  | 用户guid |
| 16 | name_or_nickname | text |  |  |  | 姓名 |
| 17 | sms_unsubscribe_info | mediumtext |  |  |  | 退订信息 |
| 18 | can_unsubscribe | int | 32 |  | 0 | 是否可以退订（0：不可退订 1：可以退订）（短信发送时用） |
| 19 | receipt_time | datetime |  |  |  | 短信结果回复时间 |
| 20 | creater | varchar |  |  |  | 创建者 |

### 标签表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 标签名称 |
| 2 | type_name | varchar | 255 |  |  | 父id |
| 3 | pid | int | 11 |  |  |  |
| 4 | label_guid | varchar | 64 |  |  | 父节点名称 |
| 5 | pname | varchar | 255 |  |  | 等级 |
| 6 | level | int | 11 |  |  | 备注 |
| 7 | tips | varchar | 255 |  | 0 | 标签类型(0:系统标签; 1:自定义标签; 2:第三方标签) |
| 8 | type | int | 11 |  | 1 | 状态(0:停用;1:启用) |
| 9 | status | int | 11 |  | 1 | 检索方式 |
| 10 | search_type | int | 11 |  |  | 创建时间 |
| 11 | create_time | datetime |  |  |  | 创建者用户名 |
| 12 | creater | varchar | 32 |  |  |  |
| 13 | update_time | datetime |  |  |  |  |
| 14 | updater | varchar | 32 |  | 0 | 是否是叶子节点(0:不是1：是) |
| 15 | foliage | int | 11 |  |  | searchType为1时，对标签的判断值做约束条件的json内容（maxlength:最大长度, type: 输入值类型, dv:默认值, tip:单位） |
| 16 | detail | varchar | 255 |  |  |  |
| 17 | label_val | decimal | 19,2 |  |  | 标签的互斥属性（0：可以存在多个值；1：只能拥有一个值） |
| 18 | mutex | int | 11 |  |  |  |

### 推广传播表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | 自动编号,自动增长 |
| 2 | apid | varchar | 32 |  |  | apid（文案ART/问卷QST/活动ACT/海报POS + yyyymmddhhMMssSSS + 3位随机数） |
| 3 | title | varchar | 100 |  |  | 标题 |
| 4 | status | int | 11 |  |  | 状态（0：草稿；1：启用；2：停用；3：删除） |
| 5 | type | int | 11 |  | 0 | 类型（0：文案；1：问卷；2：活动；3：海报） |
| 6 | cnt | int | 11 |  | 0 | 访问次数 |
| 7 | create_time | datetime |  |  | 0 | 创建时间 |
| 8 | creater | varchar | 32 |  |  | 创建者用户名 |
| 9 | update_time | datetime |  |  |  | 更新时间 |
| 10 | updater | varchar | 32 |  |  | 更新者用户名 |
| 11 | picid | bigint | 20 |  |  |  |
| 12 | merge_type | int | 11 |  |  | 是否覆盖（0：自动覆盖，1：手动覆盖，2：不覆盖） |
| 13 | end_time | date | date |  | 2 | 截止时间 |
| 14 | end_flag | int | 11 |  |  | 判断是否为时间截止用，1：时间截止 |
| 15 | isrepeat | int | 11 |  |  | 允许重复：0：不允许，1：允许 |
| 16 | share_desc | varchar | 255 |  |  | 分享链接简述 |
| 17 | share_img_url | text |  |  |  | 分享链接图片url |

### 邮件任务

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | email_id | varchar | 32 |  |  | 邮件ID（生成规则：YYYYMMDD24hmmsssss+3位数字随机数，退订/统计用） |
| 3 | sender_name | varchar | 50 |  |  | 发送方 |
| 4 | sender_email | varchar | 50 |  |  | 发送方地址 |
| 5 | reply_to_email | varchar | 100 |  |  | 回复邮件地址 |
| 6 | group_id | varchar | 32 |  |  | 用户组ID（（GROYYYYMMDD24hmmsssss+3位数字随机数）） |
| 7 | plan_time | datetime |  |  |  | 计划发送时间 |
| 8 | run_time | datetime |  |  |  | 实际发送时间 |
| 9 | contact_plan_id | int | 11 |  |  | 联络任务ID |
| 10 | topic | varchar | 50 |  |  | 邮件主题 |
| 11 | status | tinyint | 1 |  |  | 状态（0：未生成 1：已生成 ） |
| 12 | template_id | int | 11 |  |  | 模板id |
| 13 | total_count | int | 11 | 否 |  | 任务总条数 |
| 14 | sendnow_flag | int | 11 |  |  | 是否立即发送（0：否 1：是） |
| 15 | mail_unsubscribe_info | varchar | 255 |  |  | 退订内容 |
| 16 | user_guids | longtext |  |  |  | 存当前任务的userGuids（拼好的sql语句） |

### 短信任务 

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | sms_id | varchar | 32 |  |  | 短信任务ID（生成规则：SMSYYYYMMDD24hmmsssss+3位数字随机数，退订/统计用） |
| 3 | sms_code | varchar | 20 |  |  | 短信模板编号 |
| 4 | status | int | 11 |  |  | 状态（0：未生成 1：已生成2：草稿箱 ） |
| 5 | create_time | datetime |  |  |  | 生成时间 |
| 6 | group_id | varchar | 32 |  |  | 用户组ID（（GROYYYYMMDD24hmmsssss+3位数字随机数）） |
| 7 | param_string | mediumtext |  |  |  | 参数 |
| 8 | plan_time | datetime |  |  |  | 计划发送时间 |
| 9 | run_time | datetime |  |  |  | 实际发送时间 |
| 10 | contact_plan_id | int | 11 |  |  | 联络任务ID |
| 11 | topic | varchar | 50 |  |  | 联络主题 |
| 12 | content | varchar | 500 | 否 |  | 短信内容 |
| 13 | total_count | bigint | 20 |  |  | 任务总条数 |
| 14 | sendnowflag | int | 2 |  |  | 是否立即发送（0：否 1：是） |
| 15 | sms_unsubscribe_info | varchar | 255 |  |  | 短信结尾退订内容 |
| 16 | user_guids | longtext |  |  |  | 存当前任务的userGuids（拼好的sql语句） |

