# 财务管理模块 数据字典

## 模块概述

**业务模块**: 财务管理  
**关键词**: account, transaction, payment, invoice, 账户, 交易, 支付, 发票  
**相关表数量**: 77  
**涉及系统**: 10个  

## 相关表清单

| 序号 | 表名 | 所属系统 | 字段数 | 说明 |
|------|------|----------|--------|------|
| 1 | 发票申请表 | TAXUS系统 | 42 |  |
| 2 | 发票表 | TAXUS系统 | 33 |  |
| 3 | 公募支付配置表 | 财务支付系统 | 30 |  |
| 4 | 发票表 | 财务支付系统 | 29 |  |
| 5 | 账户表 | TAXUS系统 | 29 |  |
| 6 | 发票表 | CRM客户关系管理系统 | 26 |  |
| 7 | 支付信息表 | HORN系统 | 26 |  |
| 8 | 支付账单下载配置表 | TAXUS系统 | 25 |  |
| 9 | 机构账户关联表 | HORN系统 | 24 |  |
| 10 | 发票表 | 联劝CRM系统 | 23 |  |
| 11 | 线下捐赠发票表 | TAXUS系统 | 23 |  |
| 12 | 发票同步信息表 | TAXUS系统 | 23 |  |
| 13 | 账户表 | 财务支付系统 | 21 |  |
| 14 | 支付宝对账单表 | TAXUS系统 | 20 |  |
| 15 | 支付宝原始账单模板表 | 业务管理系统 | 19 |  |
| 16 | 支付宝合并开票申请表 | 业务管理系统 | 19 |  |
| 17 | 支付宝账单下载配置表 | TAXUS系统 | 19 |  |
| 18 | 批量支付确认详情表 | 业务管理系统 | 18 |  |
| 19 | t_egg_invoice_temp_info | EGG系统 | 17 |  |
| 20 | 错误发票表 | TAXUS系统 | 17 |  |
| 21 | 实体无账户外部订单表 | 财务支付系统 | 15 |  |
| 22 | 发票邮寄表 | TAXUS系统 | 15 |  |
| 23 | invoice_info | CAS认证系统 | 15 |  |
| 24 | 机构账户表 | CRM客户关系管理系统 | 14 |  |
| 25 | 发票邮寄情报表 | 财务支付系统 | 13 |  |
| 26 | 开放式支付配置表 | HORN系统 | 13 |  |
| 27 | t_xxbz_invoice_temp_info | 小小暴走系统 | 13 |  |
| 28 | 发票申请表 | 财务支付系统 | 12 |  |
| 29 | （新）公募基金支付设置表  | TAXUS系统 | 12 |  |
| 30 | 行政管理费后支付订单的明细流水表 | TAXUS系统 | 11 |  |
| 31 | 行政管理费后支付订单表  | TAXUS系统 | 11 |  |
| 32 | 账户机构余额表 | CRM客户关系管理系统 | 11 |  |
| 33 | 发票信息表 | CRM客户关系管理系统 | 11 |  |
| 34 | 发票批次表 | TAXUS系统 | 10 |  |
| 35 | 支付操作日志表 | 财务支付系统 | 9 |  |
| 36 | 财务批量支付确认表 | 业务管理系统 | 9 |  |
| 37 | 发票打印表 | TAXUS系统 | 9 |  |
| 38 | 账户用户关系表 | CRM客户关系管理系统 | 9 |  |
| 39 | 机构账户余额表 | CRM客户关系管理系统 | 9 |  |
| 40 | 支付类型月统计表 | 财务支付系统 | 8 |  |
| 41 | 机构银行账户表 | 业务管理系统 | 8 |  |
| 42 | 发票统计表 | 统计分析系统 | 8 |  |
| 43 | 支付渠道日统计表 | TAXUS系统 | 8 |  |
| 44 | 联劝月捐赠支付渠道统计表 | TAXUS系统 | 8 |  |
| 45 | 支付类型月统计表 | TAXUS系统 | 8 |  |
| 46 | 支付类型年月统计表 | 财务支付系统 | 7 |  |
| 47 | 发票订单表 | 联劝CRM系统 | 7 |  |
| 48 | 发票标签处理表 | 联劝CRM系统 | 7 |  |
| 49 | 基金年度账户表 | TAXUS系统 | 7 |  |
| 50 | 支付账单读取时间记录 | TAXUS系统 | 7 |  |
| 51 | 支付账单配置变量表 | TAXUS系统 | 7 |  |
| 52 | 交易流水表 | TAXUS系统 | 7 |  |
| 53 | 年度账户表 | TAXUS系统 | 7 |  |
| 54 | 账户钱包表 | CRM客户关系管理系统 | 7 |  |
| 55 | 发票邮寄方式表 | HORN系统 | 7 |  |
| 56 | 发票申请周表定时生成记录  | 财务支付系统 | 6 |  |
| 57 | 退款报销账户支出预变更记录表 | TAXUS系统 | 6 |  |
| 58 | 发票申请周表定时生成记录 | HORN系统 | 6 |  |
| 59 | invoice_title_info | CAS认证系统 | 6 |  |
| 60 | 交易流水表 | 财务支付系统 | 5 |  |
| 61 | t_egg_team_FundAccount | EGG系统 | 5 |  |
| 62 | 支付类型表 | TAXUS系统 | 5 |  |
| 63 | 支付宝与腾讯公益开票待核销交易流水表 | TAXUS系统 | 5 |  |
| 64 | 订单和发票申请关联表 | 财务支付系统 | 4 |  |
| 65 | 发票csv周记录创建历史表  | 财务支付系统 | 4 |  |
| 66 | 订单和发票申请关联表 | TAXUS系统 | 4 |  |
| 67 | 账单账户配置表 | TAXUS系统 | 4 |  |
| 68 | 发票同步事件表 | HORN系统 | 4 |  |
| 69 | 交易代码表 | 财务支付系统 | 3 |  |
| 70 | 发票申请码表 | 财务支付系统 | 3 |  |
| 71 | 支付渠道表 | 财务支付系统 | 3 |  |
| 72 | 支付类型表 | 统计分析系统 | 3 |  |
| 73 | 发票自取表 | TAXUS系统 | 3 |  |
| 74 | 发票定时操作时间记录表 | TAXUS系统 | 3 |  |
| 75 | 支付类别名称对应表 | TAXUS系统 | 3 |  |
| 76 | 交易代码表 | TAXUS系统 | 3 |  |
| 77 | 爱心传递实名支付记录表 | 业务管理系统 | 0 |  |

## 主要表结构详情

### 发票申请表 (TAXUS系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | invoiceId | int | 11 | 是 |  | 发票ID |
| 3 | postId | int | 11 | 否 |  | 邮寄情报ID |
| 4 | invoiceTitle | varchar | 128 | 否 |  | 发票抬头 |
| 5 | donateProjectName | text | 0 | 否 |  | 捐赠项目 |
| 6 | amount | decimal | 15,2 | 否 |  | 发票金额 |
| 7 | handlePerson | varchar | 32 | 是 |  | 开票人姓名 |
| 8 | verfiedPerson | varchar | 32 | 是 |  | 复核人姓名 |
| 9 | status | int | 1 | 否 | 0 | 申请处理状态（0：待开 1：正常（已开具）；2：开票中 3：开票失败；4：审核中；5：审核不通过） |
| 10 | applyTime | datetime |  | 否 |  | 发票申请时间 |
| 11 | remark | text | 0 | 是 |  | 备注 |
| 12 | applyCode | varchar | 8 | 否 |  | 申请编号（联劝网发票申请同步过来的，基金会发票申请不需要） |
| 13 | invoiceType | tinyint | 4 |  |  | 票据类型（0：匿名；1：个人；2：企业；） |
| 14 | cardType | tinyint | 4 |  |  | 证件号类型（1：身份证；2：护照号） |
| 15 | creditCode | varchar | 64 |  |  | 统一社会信用代码 |
| 16 | isNew | tinyint | 4 | 否 | 2 | 是否是新发票（1：不是；2：是） |
| 17 | itemId | bigint | 20 |  |  | 票据内容id |
| 18 | receiveType | tinyint | 4 |  |  | 收款方式:1 现金,2 转账,3 公共支付 平台. 4 POS 交款 |
| 19 | errorMsg | text | 0 |  |  | 错误信息 |
| 20 | invoiceTime | datetime | 0 |  |  | 发票时间 |
| 21 | serialNumber | varchar | 64 |  |  | 发票工程编号 |
| 22 | outFlag | tinyint | 4 | 否 | 0 | 其他申请来源（0：基金会电子票据；1：支付宝；2：腾讯；3：先收款后开票；4：字节跳动；） |
| 23 | syncFlag | tinyint | 4 | 否 | 0 | 同步标记，其他申请来源用（0：未同步；1：已同步；2：同步失败；3：作废已同步；4：作废同步失败） |
| 24 | syncMsg | text | 0 |  |  | 同步票据失败说明 |
| 25 | uid | varchar | 256 |  |  | 支付宝参数：用户ID |
| 26 | tradeNo | varchar | 256 |  |  | 支付宝参数：交易号 |
| 27 | tradeDate | datetime | 0 |  |  | 支付宝参数：交易日期 |
| 28 | ngoName | varchar | 256 |  |  | 支付宝参数：机构名称 |
| 29 | ngoId | varchar | 256 |  |  | 支付宝参数：机构ID |
| 30 | backInvoiceUrl | varchar | 256 |  |  | 支付宝同步回传发票地址（无实际用处，仅记录） |
| 31 | iuid | varchar | 256 |  |  | 腾讯参数：开票号（发票申请id） |
| 32 | openindex | int | 11 |  |  | 腾讯参数：重开序号 |
| 33 | uscc | varchar | 256 |  |  | 腾讯参数：统一社会信用代码（企业开票用） |
| 34 | pid | int | 11 |  |  | 腾讯参数：开票项目 ID |
| 35 | fundid | int | 11 |  |  | 腾讯参数：开票公募 ID |
| 36 | userid | varchar | 256 |  |  | 腾讯参数：捐款人 ID |
| 37 | openid | varchar | 256 |  |  | 腾讯参数：小程序 openid |
| 38 | number | int | 11 |  |  | 腾讯参数：捐款笔数 |
| 39 | tradeNos | text | 0 |  |  | 腾讯流水号,拼接 |
| 40 | details | text | 0 |  |  | 腾讯参数：捐款明细JSONArray格式 |
| 41 | reopenFlg | tinyint | 0 | 否 |  | 是否已经重开（0：没有重开；1：已经重开；2：商家未重开） |
| 42 | gybbFlg | tinyint | 0 | 否 |  | 是否是公益宝贝或者消费捐开票（0：否；1：是；） |

### 发票表 (TAXUS系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | batchId | int | 11 | 否 |  | 发票批次ID |
| 3 | invoiceCode | varchar | 16 | 否 |  | 发票编号 |
| 4 | status | int | 2 | 否 | 0 | 发票状态（0：待开 1：正常（已开具） 2：作废；4：作废审核中） |
| 5 | printTime | datetime |  | 是 |  | 发票打印时间（发票开具时间） |
| 6 | invalidTime | datetime |  | 是 |  | 发票作废时间 |
| 7 | invalidUser | varchar | 32 | 是 |  | 发票作废操作者 |
| 8 | invoiceTitle | varchar | 128 | 是 |  | 发票抬头 |
| 9 | invoiceTime | datetime |  | 是 |  | 发票时间 |
| 10 | item1 | varchar | 128 | 是 |  | 捐赠项目1 |
| 11 | amount1 | decimal | 15,2 | 是 |  | 发票金额1 |
| 12 | item2 | varchar | 128 | 是 |  | 捐赠项目2 |
| 13 | amount2 | decimal | 15,2 | 是 |  | 发票金额2 |
| 14 | item3 | varchar | 128 | 是 |  | 捐赠项目3 |
| 15 | amount3 | decimal | 15,2 | 是 |  | 发票金额3 |
| 16 | handlePerson | varchar | 32 | 是 |  | 开票人姓名 |
| 17 | verfiedPerson | varchar | 32 | 是 |  | 复核人姓名 |
| 18 | remark | varchar | 256 | 是 |  | 备注 |
| 19 | postId | int | 11 | 是 |  | 实际打印邮寄情报表的ID |
| 20 | invalidInvoiceId | int | 11 | 是 |  | 替换的作废发票ID |
| 21 | invalidReason | varchar | 255 | 是 |  | 作废理由 |
| 22 | selfGetId | int | 11 | 是 |  | 自取ID |
| 23 | serialNumber | varchar | 64 |  |  | 发票工程编号 |
| 24 | invoiceImg | varchar | 255 |  |  | 发票图片 |
| 25 | invoicePdf | varchar | 255 |  |  | 发票pdf |
| 26 | errorMsg | text | 0 |  |  | 错误信息 |
| 27 | isSend | tinyint | 4 |  |  | 是否发送短信（0：未发送；1：已发送；2：无需发送） |
| 28 | shortUrl | varchar | 500 |  |  | 短链接 |
| 29 | invoiceBantchCode | varchar | 255 |  |  | 票据代码 |
| 30 | verify | varchar | 64 |  |  | 校验码 |
| 31 | transformPdf | varchar | 255 |  |  | png图片转为pdf格式 |
| 32 | invoicePdfOss | varchar | 255 |  |  | 发票PDF文件保存到OSS存储 |
| 33 | pdfOssFlag | int | 0 |  |  | 发票PDF文件保存到OSS存储标志（0：未同步；1：已同步） |

### 公募支付配置表 (财务支付系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | payType | varchar | 20 | 否 |  | 支付方式 |
| 3 | partner | varchar | 32 | 否 |  | 合作身份者ID |
| 4 | subMchId | varchar | 32 | 是 |  | 子商户号 |
| 5 | key | varchar | 32 | 是 |  | 商户的私钥（签名方式MD5）——新接口不再需要该参数 |
| 6 | private_key | text |  | 是 |  | 商户的私钥（签名方式RSA） |
| 7 | ali_public_key | text |  | 是 |  | 支付宝的公钥（签名方式RSA） |
| 8 | paymentType | varchar | 1 | 否 |  | 支付类型 |
| 9 | notifyUrl | varchar | 255 | 否 |  | 服务器异步通知页面路径 |
| 10 | returnUrl | varchar | 255 | 否 |  | 页面跳转同步通知页面路径 |
| 11 | seller | varchar | 40 | 否 |  | 卖家支付宝帐户 |
| 12 | logPath | varchar | 255 | 否 |  | 日志文件夹路径 |
| 13 | inputCharset | varchar | 20 | 否 |  | 字符编码格式 |
| 14 | signType | varchar | 10 | 否 |  | 签名方式（MD5、RSA） |
| 15 | showFlg | varchar | 1 | 否 |  | 显示与否 |
| 16 | merchantUrl | varchar | 255 | 是 |  | 中断返回URL |
| 17 | decryptKey | varchar | 255 | 是 |  | 解密KEY |
| 18 | appId | varchar | 32 | 是 |  | 新版本手机网站支付应用ID |
| 19 | charset | varchar | 20 | 是 |  | 新版本手机网站支付编码格式 |
| 20 | format | varchar | 10 | 是 |  | 新版本手机网站支付参数格式 |
| 21 | privateKey | text |  | 是 |  | 新版本手机网站支付私钥 |
| 22 | pulicKey | text |  | 是 |  | 新版本手机网站支付公钥 |
| 23 | fundraiseEntityGuid | varchar | 32 | 否 |  | 支付实体id |
| 24 | ifCanInvoice | varchar | 1 | 否 |  | 是否可开票（0：不可 1：可） |
| 25 | holderCode | varchar | 6 | 是 |  | 订单号前缀 |
| 26 | appSecret | varchar | 32 | 是 |  | 微信公众号Secret，用于获取微信授权TOKEN |
| 27 | isDonate | varchar | 1 | 否 |  | 是否公益账号（0：不是 1：是） |
| 28 | start | date | 0 | 是 |  | 账单下载开始时间 |
| 29 | errorNum | int | 4 | 是 |  | 账单下载出错次数 |
| 30 | errorMsg | text | 0 | 是 |  | 账单下载出错msg |

### 发票表 (财务支付系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | invoiceCode | varchar | 16 | 是 |  | 发票编号 |
| 3 | status | int | 1 | 是 | 0 | 发票状态（0：待开 1：正常（已开具） 2：作废） |
| 4 | printTime | datetime |  | 是 |  | 发票打印时间（发票开具时间） |
| 5 | invalidTime | datetime |  | 是 |  | 发票作废时间 |
| 6 | invalidUser | varchar | 32 | 是 |  | 发票作废操作者 |
| 7 | invoiceTitle | varchar | 128 | 是 |  | 发票抬头 |
| 8 | item1 | text |  | 是 |  | 捐赠项目1 |
| 9 | amount1 | decimal | 15,2 | 是 |  | 发票金额1 |
| 10 | item2 | varchar | 128 | 是 |  | 捐赠项目2 |
| 11 | amount2 | decimal | 15,2 | 是 |  | 发票金额2 |
| 12 | item3 | varchar | 128 | 是 |  | 捐赠项目3 |
| 13 | amount3 | decimal | 15,2 | 是 |  | 发票金额3 |
| 14 | handlePerson | varchar | 32 | 是 |  | 开票人姓名 |
| 15 | verfiedPerson | varchar | 32 | 是 |  | 复核人姓名 |
| 16 | remark | varchar | 256 | 是 |  | 备注 |
| 17 | invalidInvoiceId | int | 11 | 是 |  | 替换的作废发票ID |
| 18 | invalidReason | varchar | 255 | 是 |  | 作废理由 |
| 19 | applyTime | datetime |  | 否 |  | 发票申请时间 |
| 20 | updateTime | datetime |  | 否 |  | 发票申请信息修改时间 |
| 21 | applyCode | varchar | 8 | 否 |  | 发票申请码 |
| 22 | synchronStatus | int | 2 | 否 |  | 同步状态（0未同步，1同步完成，2同步出错） |
| 23 | message | varchar | 255 | 否 |  | 状态更新信息 |
| 24 | errorMsg | text | 0 | 是 |  | 错误原因 |
| 25 | serialNumber | varchar | 64 | 是 |  | 发票系统编号 |
| 26 | isSend | tinyint | 4 | 是 | 0 | 是否发送短信（0：未发送；1：已发送；2：无需发送） |
| 27 | invoiceImg | varchar | 500 | 是 |  | 发票图片 |
| 28 | invoicePdf | varchar | 500 | 是 |  | 发票pdf |
| 29 | shortUrl | varchar | 500 | 是 |  | 短链接地址 |

### 账户表 (TAXUS系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | account | varchar | 32 | 否 |  | 账号 |
| 3 | entityId | int | 11 | 否 |  | 实体ID |
| 4 | accountTypeId | int | 11 | 否 |  | 账户类型ID |
| 5 | createDate | datetime |  | 否 |  | 开户时间 |
| 6 | validDate | datetime |  | 是 |  | 账户有效期（临时账户用） |
| 7 | deleteDate | datetime |  | 是 |  | 销户时间 |
| 8 | accountStatusId | int | 11 | 否 |  | 账户状态ID |
| 9 | balance | decimal | 15,2 | 否 |  | 账户余额 |
| 10 | frozon | decimal | 15,2 | 否 |  | 冻结资金 |
| 11 | currencyId | int | 11 | 否 |  | 币种类型ID |
| 12 | ruleId | int | 11 | 否 | 0 | 自动转账规则ID |
| 13 | accountHolderId | int | 11 | 是 |  | 开户人实体ID（活动实体ID、项目实体ID等） |
| 14 | accountProperty | int | 1 | 否 | 1 | 账户属性（0：匿名捐款 1：实名捐款 2：筹款 3：报名费 4:配捐(第三方平台) 5：配捐(企业赞助商)  6：抽签费 7：行政经费 8:借款 9：其他 10：可拨付经费 11：接受拨付 12：年度 |
| 15 | overdraw | int | 1 | 否 | 0 | 能否透支（0：不可透支 1：可透支） |
| 16 | fundType | int | 1 | 否 | 0 | 筹款类型（0：普通筹款 1：筹款工具-爱扑满 2：筹款工具-OMP 3：义卖 4：筹款工具-联合捐）当检索条件时，务必跟accountProperty=2一起使用 |
| 17 | inAllAmount | decimal | 15,2 | 否 | 0 | 进账总额 |
| 18 | outAllAmount | decimal | 15,2 | 否 | 0 | 出帐总额 |
| 19 | inCount | int | 11 | 否 | 0 | 进账次数 |
| 20 | outCount | int | 11 | 否 | 0 | 出账次数 |
| 21 | adminFeeType | int | 11 | 否 | 0 | 行政管理费提取类型（0：不提取 1：固定比例提取 2：累进金额按约定比例提取 3：全额按约定比例提取） |
| 22 | changeYearAccountFlag | int | 11 | 否 | 0 | 年度账户自动切换标记（0：不切换 1：自然年度切换（1月1日0时0分0秒） 2：周期年度切换（开户时间的下一年的0时0分0秒）） |
| 23 | limitAmount | decimal | 15,2 | 否 | 0 | 账户限额（0表示无限额） |
| 24 | statusFlag | int | 11 | 否 | 0 | 筹款账户状态标志（0：正常 1：筹款已满 2：已过筹款期 3：强行中止筹款） |
| 25 | specifiedPurposeFlag | int | 2 | 否 | 0 | 账户专款专用标志（0：专款专用 1：进资金池）--现在只对活动、项目有效 |
| 26 | platformType | int | 2 | 否 | 0 | 筹款账户平台类型（0：联劝网平台 1：第三方平台 2：总 3：线下捐赠渠道 10：月捐（过联劝网平台）） |
| 27 | limitAmountFlag | decimal | 15,2 | 否 | 0 | 账户限额是否有效（0：无效 1：有效） |
| 28 | adminFeeGetMode | int | 2 | 否 | 0 | 行政管理费提取方式（0：先提 1：后提） |
| 29 | flag | int | 1 | 是 | 0 | 联劝网分离时，账户迁移标志位（0：无需迁移 1：已经迁移），现在已经无用 |

