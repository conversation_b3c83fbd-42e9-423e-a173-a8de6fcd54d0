# 订单管理模块 数据字典

## 模块概述

**业务模块**: 订单管理  
**关键词**: order, 订单  
**相关表数量**: 51  
**涉及系统**: 7个  

## 相关表清单

| 序号 | 表名 | 所属系统 | 字段数 | 说明 |
|------|------|----------|--------|------|
| 1 | 订单表 | TAXUS系统 | 65 |  |
| 2 | 联劝网同步订单表 | TAXUS系统 | 64 |  |
| 3 | 订单表 | 财务支付系统 | 42 |  |
| 4 | 2015订单表 | TAXUS系统 | 34 |  |
| 5 | 2016订单表 | TAXUS系统 | 34 |  |
| 6 | 预订单表 | 财务支付系统 | 33 |  |
| 7 | 成功订单表 | 财务支付系统 | 33 |  |
| 8 | 订单推送表 | 财务支付系统 | 27 |  |
| 9 | 商品主订单表 | HORN系统 | 18 |  |
| 10 | 义卖商品预订单表 | HORN系统 | 17 |  |
| 11 | 实体无账户外部订单表 | 财务支付系统 | 15 |  |
| 12 | 外部订单表 | HORN系统 | 15 |  |
| 13 | 商品从订单表 | HORN系统 | 13 |  |
| 14 | 订单捐赠对象变更记录 | 财务支付系统 | 12 |  |
| 15 | t_egg_reg_order | EGG系统 | 11 |  |
| 16 | 行政管理费后支付订单的明细流水表 | TAXUS系统 | 11 |  |
| 17 | 行政管理费后支付订单表  | TAXUS系统 | 11 |  |
| 18 | 报名费主订单表 | HORN系统 | 11 |  |
| 19 | t_xxbz_reg_order | 小小暴走系统 | 11 |  |
| 20 | 订单请求配置表 | 财务支付系统 | 10 |  |
| 21 | 商户月度信息订单关联表 | TAXUS系统 | 10 |  |
| 22 | 订单表 | HORN系统 | 10 |  |
| 23 | 商品订单表 | 财务支付系统 | 9 |  |
| 24 | t_egg_reg_order_sub | EGG系统 | 9 |  |
| 25 | 报名费从订单表 | HORN系统 | 9 |  |
| 26 | t_xxbz_reg_order_sub | 小小暴走系统 | 9 |  |
| 27 | 报名费子订单表 | 财务支付系统 | 8 |  |
| 28 | 订单请求表 | 财务支付系统 | 7 |  |
| 29 | 发票订单表 | 联劝CRM系统 | 7 |  |
| 30 | 待发订单邮件信息表 | 财务支付系统 | 6 |  |
| 31 | 公益三小时订单关联记录表 | 财务支付系统 | 6 |  |
| 32 | 订单处理信息表 | 财务支付系统 | 5 |  |
| 33 | 义卖订单存储过程执行错误记录表 | HORN系统 | 5 |  |
| 34 | 订单和发票申请关联表 | 财务支付系统 | 4 |  |
| 35 | 订单和发票申请关联表 | TAXUS系统 | 4 |  |
| 36 | 机构项目外部筹款订单最后导入时间记录表 | HORN系统 | 4 |  |
| 37 | 订单和证书关联表 | 财务支付系统 | 3 |  |
| 38 | 订单存储过程状态表 | 财务支付系统 | 3 |  |
| 39 | 非测试订单url配置表 | 财务支付系统 | 3 |  |
| 40 | 订单存储过程状态表 | TAXUS系统 | 3 |  |
| 41 | 订单类别名称对应表 | TAXUS系统 | 3 |  |
| 42 | 外部订单表处理最大id表 | 财务支付系统 | 2 |  |
| 43 | 对账未成功异常订单捐赠时间统计表 | 财务支付系统 | 2 |  |
| 44 | 订单同步操作时间记录表 | TAXUS系统 | 2 |  |
| 45 | 异常订单表 | TAXUS系统 | 2 |  |
| 46 | 义卖订单存储过程标记表 | HORN系统 | 2 |  |
| 47 | 商品从订单表  | 业务管理系统 | 0 |  |
| 48 | 订单特殊化处理表 | 业务管理系统 | 0 |  |
| 49 | 商品主订单表 | 业务管理系统 | 0 |  |
| 50 | 商品预订单表 | 业务管理系统 | 0 |  |
| 51 | 义卖订单存储过程标记表 | 业务管理系统 | 0 |  |

## 主要表结构详情

### 订单表 (TAXUS系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderNo | varchar | 32 | 否 |  | 订单号 |
| 3 | orderName | varchar | 128 | 否 |  | 订单内容(名称) |
| 4 | entityId | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 5 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 6 | toAccount | varchar | 255 | 否 |  | 受捐账户（活动报名费账户） |
| 7 | amount | decimal | 15,2 | 否 |  | 金额 |
| 8 | donateTime | datetime |  | 否 |  | 捐赠时间 |
| 9 | message | varchar | 1024 | 是 |  | 留言 |
| 10 | orderType | tinyint | 2 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 6：资助费 9：现金充值 20：报名费退款 21：捐款退款 25：抽签费退款 26：资助费退 50：调账（订单状态为0） ）（2，3，4在支付系统中有使用， |
| 11 | orderStatus | tinyint | 2 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 12 | invoiceBalance | decimal | 15,2 | 否 |  | 可开发票余额 |
| 13 | phone | varchar | 11 | 是 |  | 手机号码 |
| 14 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 15 | callBackUrl | varchar | 255 | 否 |  | 返回URL |
| 16 | autoCallBackUrl | varchar | 255 | 否 |  | 主动回调URL |
| 17 | searchCode | varchar | 16 | 是 |  | 查询码 |
| 18 | payType | tinyint | 2 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 8：移动和包 9：苏宁易付宝 10：美团钱袋宝 11：微博钱包 80：企业网银（线下）81：个人网银（线下）8 |
| 19 | accessType | tinyint | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加） |
| 20 | tradeDetailId | bigint | 20 | 是 |  | 交易流水ID |
| 21 | serialNo | bigint | 20 | 是 |  | 资金序列号 |
| 22 | invoiceStatus | tinyint | 2 | 否 | 0 | 发票申请状态（0：未申请 1：已申请 2：已开具 3:已邮寄 4：线下已开 5：已自取 6：已退回 7：不能开票 8、9、10个人中心代码里已用 11：项目发起机构统一开票） |
| 23 | bankTradeNo | varchar | 64 | 是 |  | 银行交易流水号 |
| 24 | businessId | varchar | 255 | 是 |  | 商户系统编号（比如活动ID） |
| 25 | templetJsonData | varchar | 512 | 是 |  | 模板数据 |
| 26 | templetType | varchar | 255 | 是 |  | 模板类型 |
| 27 | donateType | tinyint | 2 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：义卖 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 28 | otherSiteReturnData | varchar | 255 | 是 |  | 需要记录的第三方网站返回的特殊业务数据（点融网：点融网的注册用户名） |
| 29 | sourceAccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 30 | checkFlag | tinyint | 2 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，6:支出/提现， 7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 11：线下捐款 12：报名捐 ） |
| 31 | createTime | datetime |  | 是 |  | 订单生成时间（对账成功时间） |
| 32 | name | varchar | 64 | 是 |  | 捐赠者姓名 |
| 33 | ipAddress | varchar | 64 | 是 |  | ip地址 |
| 34 | ipType | int | 1 | 是 | 0 | ip类型（0：ipv4 1：ipv6） |
| 35 | inCount | int | 11 | 是 | 0 | 合并账单数 |
| 36 | fundEntityId | int | 11 | 是 | 0 | 资金池 |
| 37 | poolEntityId | int | 11 | 是 | 0 | 子资金池 |
| 38 | intItem3 | int | 11 | 是 | 0 | 预留的项目3 |
| 39 | intItem4 | int | 11 | 是 | 0 | 预留的项目4 |
| 40 | frozenStatus | int | 11 | 是 | 0 | 0：未冻结；1：已冻结 |
| 41 | inCount | int | 11 | 是 | 1 | 合并账单数 |
| 42 | dateItem1 | datetime |  | 是 |  | 预留日期项目1 |
| 43 | dateItem2 | datetime |  | 是 |  | 预留日期项目2 |
| 44 | dateItem3 | datetime |  | 是 |  | 预留日期项目3 |
| 45 | dateItem4 | datetime |  | 是 |  | 预留日期项目4 |
| 46 | strItem1 | varchar | 64 | 是 |  | 预留字符串1 |
| 47 | strItem2 | varchar | 64 | 是 |  | 预留字符串2 |
| 48 | strItem3 | varchar | 64 | 是 |  | 预留字符串3 |
| 49 | strItem4 | varchar | 64 | 是 |  | 预留字符串4 |
| 50 | strItem5 | varchar | 128 | 是 |  | 预留字符串5 |
| 51 | strItem6 | varchar | 128 | 是 |  | 预留字符串6 |
| 52 | strItem7 | varchar | 128 | 是 |  | 预留字符串7 |
| 53 | strItem8 | varchar | 128 | 是 |  | 预留字符串8 |
| 54 | strItem9 | varchar | 255 | 是 |  | 预留字符串9 |
| 55 | strItem10 | varchar | 255 | 是 |  | 预留字符串10 |
| 56 | strItem11 | varchar | 255 | 是 |  | 预留字符串11 |
| 57 | strItem12 | varchar | 255 | 是 |  | 预留字符串12 |
| 58 | amountItem1 | decimal | 15,2 | 是 | 0 | 预留金额1 |
| 59 | amountItem2 | decimal | 15,2 | 是 | 0 | 预留金额2 |
| 60 | amountItem3 | decimal | 15,2 | 是 | 0 | 预留金额3 |
| 61 | amountItem4 | decimal | 15,2 | 是 | 0 | 预留金额4 |
| 62 | doubleItem1 | double |  | 是 | 0 | 预留双精度1 |
| 63 | doubleItem2 | double |  | 是 | 0 | 预留双精度2 |
| 64 | doubleItem3 | double |  | 是 | 0 | 预留双精度3 |
| 65 | doubleItem4 | double |  | 是 | 0 | 预留双精度4 |

### 联劝网同步订单表 (TAXUS系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderno | varchar | 32 | 否 |  | 订单号 |
| 3 | ordername | varchar | 128 | 否 |  | 订单内容(名称) |
| 4 | entityid | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 5 | fromaccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 6 | toaccount | varchar | 32 | 否 |  | 受捐账户（活动报名费账户） |
| 7 | amount | decimal | 15,2 | 否 |  | 金额 |
| 8 | donatetime | datetime |  | 否 |  | 捐赠时间 |
| 9 | message | varchar | 1024 | 是 |  | 留言 |
| 10 | ordertype | int | 1 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 6：资助费 9：现金充值 20：报名费退款 21：捐款退款 25：抽签费退款 26：资助费退 50：调账（订单状态为0） ）（2，3，4在支付系统中有使用， |
| 11 | orderstatus | int | 1 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 12 | phone | varchar | 11 | 是 |  | 手机号码 |
| 13 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 14 | callbackurl | varchar | 255 | 否 |  | 返回URL |
| 15 | autocallbackurl | varchar | 255 | 否 |  | 主动回调URL |
| 16 | searchcode | varchar | 16 | 是 |  | 查询码 |
| 17 | paytype | int | 1 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 80：企业网银（线下）81：个人网银（线下）82：财付通（线下）83：支付宝（线下）84：现金 85：京东众筹 |
| 18 | accesstype | int | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加） |
| 19 | tradedetailid | bigint | 20 | 是 |  | 交易流水ID |
| 20 | serialno | bigint | 20 | 是 |  | 资金序列号 |
| 21 | banktradeno | varchar | 64 | 是 |  | 银行交易流水号 |
| 22 | businessid | varchar | 255 | 是 |  | 商户系统编号（比如活动ID） |
| 23 | templetjsondata | varchar | 512 | 是 |  | 模板数据 |
| 24 | templettype | varchar | 255 | 是 |  | 模板类型 |
| 25 | donatetype | int | 1 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：义卖 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 26 | othersitereturndata | varchar | 255 | 是 |  | 需要记录的第三方网站返回的特殊业务数据（点融网：点融网的注册用户名） |
| 27 | sourceaccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 28 | checkflag | int | 1 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，6:支出/提现， 7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 11：线下捐款 12：报名捐 ） |
| 29 | createtime | datetime |  | 是 |  | 订单生成时间 |
| 30 | name | varchar | 64 | 是 |  | 捐赠者姓名 |
| 31 | ipaddress | varchar | 64 | 是 |  | ip地址 |
| 32 | iptype | int | 1 | 是 | 0 | ip类型（0：ipv4 1：ipv6） |
| 33 | intitem1 | int | 11 | 是 | 0 | 预留的项目1 |
| 34 | intitem2 | int | 11 | 是 | 0 | 预留的项目2 |
| 35 | intitem3 | int | 11 | 是 | 0 | 预留的项目3 |
| 36 | intitem4 | int | 11 | 是 | 0 | 预留的项目4 |
| 37 | intitem5 | int | 11 | 是 | 0 | 0：未冻结；1：已冻结 |
| 38 | intitem6 | int | 11 | 是 | 1 | 合并账单数 |
| 39 | dateitem1 | datetime |  | 是 |  | 预留日期项目1 |
| 40 | dateitem2 | datetime |  | 是 |  | 预留日期项目2 |
| 41 | dateitem3 | datetime |  | 是 |  | 预留日期项目3 |
| 42 | dateitem4 | datetime |  | 是 |  | 预留日期项目4 |
| 43 | stritem1 | varchar | 64 | 是 |  | 预留字符串1 |
| 44 | stritem2 | varchar | 64 | 是 |  | 预留字符串2 |
| 45 | stritem3 | varchar | 64 | 是 |  | 预留字符串3 |
| 46 | stritem4 | varchar | 64 | 是 |  | 预留字符串4 |
| 47 | stritem5 | varchar | 128 | 是 |  | 预留字符串5 |
| 48 | stritem6 | varchar | 128 | 是 |  | 预留字符串6 |
| 49 | stritem7 | varchar | 128 | 是 |  | 预留字符串7 |
| 50 | stritem8 | varchar | 128 | 是 |  | 预留字符串8 |
| 51 | stritem9 | varchar | 255 | 是 |  | 预留字符串9 |
| 52 | stritem10 | varchar | 255 | 是 |  | 预留字符串10 |
| 53 | stritem11 | varchar | 255 | 是 |  | 预留字符串11 |
| 54 | stritem12 | varchar | 255 | 是 |  | 预留字符串12 |
| 55 | amountitem1 | decimal | 15,2 | 是 | 0 | 预留金额1 |
| 56 | amountitem2 | decimal | 15,2 | 是 | 0 | 预留金额2 |
| 57 | amountitem3 | decimal | 15,2 | 是 | 0 | 预留金额3 |
| 58 | amountitem4 | decimal | 15,2 | 是 | 0 | 预留金额4 |
| 59 | doubleitem1 | double |  | 是 | 0 | 预留双精度1 |
| 60 | doubleitem2 | double |  | 是 | 0 | 预留双精度2 |
| 61 | doubleitem3 | double |  | 是 | 0 | 预留双精度3 |
| 62 | doubleitem4 | double |  | 是 | 0 | 预留双精度4 |
| 63 | flag | int | 1 | 是 | 0 | 0:正常 1：异常 |
| 64 | errmsg | text |  | 是 |  | 错误信息 |

### 订单表 (财务支付系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderNo | varchar | 32 | 否 |  | 订单号 |
| 3 | orderName | varchar | 128 | 否 |  | 订单内容(名称) |
| 4 | entityId | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 5 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 6 | toAccount | varchar | 255 | 否 |  | 受捐账户（活动报名费账户） |
| 7 | amount | decimal | 15,2 | 否 |  | 金额 |
| 8 | donateTime | datetime |  | 否 |  | 捐赠时间 |
| 9 | message | varchar | 1024 | 是 |  | 留言 |
| 10 | orderType | int | 1 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 20：报名费退款 21：捐款退款 25：抽签费退款）（2，3，4在支付系统中有使用，禁止使用） |
| 11 | orderStatus | int | 1 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 12 | invoiceBalance | decimal | 15,2 | 否 |  | 可开发票余额 |
| 13 | phone | varchar | 11 | 是 |  | 手机号码 |
| 14 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 15 | callBackUrl | varchar | 1024 | 否 |  | 返回URL |
| 16 | autoCallBackUrl | varchar | 1024 | 否 |  | 主动回调URL |
| 17 | searchCode | varchar | 16 | 是 |  | 查询码 |
| 18 | payType | int | 1 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 8：移动和包 9：外部接口 80：企业网银（线下）81：个人网银（线下）82：财付通（线下）83：支付宝（线下 |
| 19 | accessType | int | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加 3：外部接口） |
| 20 | tradeDetailId | bigint | 20 | 是 |  | 交易流水ID |
| 21 | invoiceStatus | int | 1 | 否 | 0 | 发票申请状态（0：未申请 1：已申请 2：已开具 3:已邮寄 4：线下已开 5：已自取 6：已退回 7：不能开票 8、9、10个人中心代码里已用 11：项目发起机构统一开票） |
| 22 | bankTradeNo | varchar | 64 | 是 |  | 银行交易流水号 |
| 23 | businessId | varchar | 32 | 是 |  | 商户系统编号（比如活动ID） |
| 24 | templetJsonData | varchar | 255 | 是 |  | 模板数据 |
| 25 | templetType | int | 1 | 是 |  | 模板类型 |
| 26 | donateType | int | 1 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：日捐 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 27 | otherSiteReturnData | varchar | 255 | 是 |  | 需要记录的第三方网站返回的特殊业务数据（点融网：点融网的注册用户名） |
| 28 | sourceAccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 29 | fundGuid | varchar | 32 | 是 |  | 公募基金会实体GUID |
| 30 | insGuid | varchar | 32 | 是 |  | 机构实体GUID |
| 31 | fundraiseEntityGuid | varchar | 32 | 是 |  | 支付实体id |
| 32 | checkFlag | int | 1 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，6:支出/提现， 7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 11：线下捐款 12：报名捐 ） |
| 33 | createTime | datetime |  | 否 |  | 订单生成时间 |
| 34 | name | varchar | 32 | 否 |  | 捐赠者姓名 |
| 35 | ipAddress | varchar | 64 | 是 |  | ip地址 |
| 36 | ipType | int | 1 | 是 | 0 | ip类型（0：ipv4 1：ipv6） |
| 37 | intItem1 | int | 1 | 是 | 0 | 是否设置为爱心人士（0：未设置；1：已设置） |
| 38 | answer | varchar | 255 | 是 |  | 留言回复 |
| 39 | ansStatus | int | 11 | 是 |  | 留言回复审核状态（0：已通过；1：待审核；2：机审不通过；3：机审通过；4：审核不通过；） |
| 40 | msgStatus | int | 11 | 是 |  | 留言审核状态（0：已通过；1：待审核；2：机审不通过；3：机审通过；4：审核不通过；） |
| 41 | entryType | varchar | 255 | 是 |  | 队伍类型 |
| 42 | poolEntityId | int | 11 | 否 | 0 | 受捐所属实体ID（活动、项目、月捐、日捐实体ID） |

### 2015订单表 (TAXUS系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderNo | varchar | 32 | 否 |  | 订单号 |
| 3 | orderName | varchar | 128 | 否 |  | 订单内容(名称) |
| 4 | entityId | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 5 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 6 | toAccount | varchar | 255 | 否 |  | 受捐账户（活动报名费账户） |
| 7 | amount | decimal | 15,2 | 否 |  | 金额 |
| 8 | donateTime | datetime |  | 否 |  | 捐赠时间 |
| 9 | message | varchar | 1024 | 是 |  | 留言 |
| 10 | orderType | tinyint | 2 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 6：资助费 9：现金充值 20：报名费退款 21：捐款退款 25：抽签费退款 26：资助费退 50：调账（订单状态为0） ）（2，3，4在支付系统中有使用， |
| 11 | orderStatus | tinyint | 2 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 12 | invoiceBalance | decimal | 15,2 | 否 |  | 可开发票余额 |
| 13 | phone | varchar | 11 | 是 |  | 手机号码 |
| 14 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 15 | callBackUrl | varchar | 255 | 否 |  | 返回URL |
| 16 | autoCallBackUrl | varchar | 255 | 否 |  | 主动回调URL |
| 17 | searchCode | varchar | 16 | 是 |  | 查询码 |
| 18 | payType | tinyint | 2 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 80：企业网银（线下）81：个人网银（线下）82：财付通（线下）83：支付宝（线下）84：现金 85：京东众筹 |
| 19 | accessType | tinyint | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加） |
| 20 | tradeDetailId | bigint | 20 | 是 |  | 交易流水ID |
| 21 | serialNo | bigint | 20 | 是 |  | 资金序列号 |
| 22 | invoiceStatus | tinyint | 2 | 否 | 0 | 发票申请状态（0：未申请 1：已申请 2：已开具 3:已邮寄 4：线下已开 5：已自取 6：已退回 7：不能开票 8、9、10个人中心代码里已用 11：项目发起机构统一开票） |
| 23 | bankTradeNo | varchar | 64 | 是 |  | 银行交易流水号 |
| 24 | businessId | varchar | 255 | 是 |  | 商户系统编号（比如活动ID） |
| 25 | templetJsonData | varchar | 512 | 是 |  | 模板数据 |
| 26 | templetType | varchar | 255 | 是 |  | 模板类型 |
| 27 | donateType | tinyint | 2 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：义卖 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 28 | otherSiteReturnData | varchar | 255 | 是 |  | 需要记录的第三方网站返回的特殊业务数据（点融网：点融网的注册用户名） |
| 29 | sourceAccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 30 | checkFlag | tinyint | 2 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，6:支出/提现， 7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 11：线下捐款 12：报名捐 ） |
| 31 | createTime | datetime |  | 是 |  | 订单生成时间 |
| 32 | name | varchar | 64 | 是 |  | 捐赠者姓名 |
| 33 | fundEntityId | int | 11 |  |  | 资金池 |
| 34 | poolEntityId | int | 11 |  |  | 子资金池 |

### 2016订单表 (TAXUS系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderNo | varchar | 32 | 否 |  | 订单号 |
| 3 | orderName | varchar | 128 | 否 |  | 订单内容(名称) |
| 4 | entityId | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 5 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 6 | toAccount | varchar | 255 | 否 |  | 受捐账户（活动报名费账户） |
| 7 | amount | decimal | 15,2 | 否 |  | 金额 |
| 8 | donateTime | datetime |  | 否 |  | 捐赠时间 |
| 9 | message | varchar | 1024 | 是 |  | 留言 |
| 10 | orderType | tinyint | 2 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 6：资助费 9：现金充值 20：报名费退款 21：捐款退款 25：抽签费退款 26：资助费退 50：调账（订单状态为0） ）（2，3，4在支付系统中有使用， |
| 11 | orderStatus | tinyint | 2 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 12 | invoiceBalance | decimal | 15,2 | 否 |  | 可开发票余额 |
| 13 | phone | varchar | 11 | 是 |  | 手机号码 |
| 14 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 15 | callBackUrl | varchar | 255 | 否 |  | 返回URL |
| 16 | autoCallBackUrl | varchar | 255 | 否 |  | 主动回调URL |
| 17 | searchCode | varchar | 16 | 是 |  | 查询码 |
| 18 | payType | tinyint | 2 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 80：企业网银（线下）81：个人网银（线下）82：财付通（线下）83：支付宝（线下）84：现金 85：京东众筹 |
| 19 | accessType | tinyint | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加） |
| 20 | tradeDetailId | bigint | 20 | 是 |  | 交易流水ID |
| 21 | serialNo | bigint | 20 | 是 |  | 资金序列号 |
| 22 | invoiceStatus | tinyint | 2 | 否 | 0 | 发票申请状态（0：未申请 1：已申请 2：已开具 3:已邮寄 4：线下已开 5：已自取 6：已退回 7：不能开票 8、9、10个人中心代码里已用 11：项目发起机构统一开票） |
| 23 | bankTradeNo | varchar | 64 | 是 |  | 银行交易流水号 |
| 24 | businessId | varchar | 255 | 是 |  | 商户系统编号（比如活动ID） |
| 25 | templetJsonData | varchar | 512 | 是 |  | 模板数据 |
| 26 | templetType | varchar | 255 | 是 |  | 模板类型 |
| 27 | donateType | tinyint | 2 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：义卖 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 28 | otherSiteReturnData | varchar | 255 | 是 |  | 需要记录的第三方网站返回的特殊业务数据（点融网：点融网的注册用户名） |
| 29 | sourceAccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 30 | checkFlag | tinyint | 2 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，6:支出/提现， 7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 11：线下捐款 12：报名捐 ） |
| 31 | createTime | datetime |  | 是 |  | 订单生成时间 |
| 32 | name | varchar | 64 | 是 |  | 捐赠者姓名 |
| 33 | fundEntityId | int | 11 |  |  | 资金池 |
| 34 | poolEntityId | int | 11 |  |  | 子资金池 |

