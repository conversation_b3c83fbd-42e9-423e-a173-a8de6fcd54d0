# 用户管理模块 数据字典

## 模块概述

**业务模块**: 用户管理  
**关键词**: user, member, 用户, 会员  
**相关表数量**: 72  
**涉及系统**: 9个  

## 相关表清单

| 序号 | 表名 | 所属系统 | 字段数 | 说明 |
|------|------|----------|--------|------|
| 1 | 用户表 | HORN系统 | 51 |  |
| 2 | user_info | CAS认证系统 | 48 |  |
| 3 | t_egg_user | EGG系统 | 36 |  |
| 4 | 用户表 | CRM客户关系管理系统 | 30 |  |
| 5 | t_xxbz_user | 小小暴走系统 | 28 |  |
| 6 | 公众号用户消息表 | 联劝CRM系统 | 27 |  |
| 7 | 公众号用户消息表 | CRM客户关系管理系统 | 27 |  |
| 8 | 捐赠用户信息收集表 | HORN系统 | 25 |  |
| 9 | 用户个人报名表 | HORN系统 | 22 |  |
| 10 | 商户用户表 | 业务管理系统 | 20 |  |
| 11 | 新增实名用户统计表 | 统计分析系统 | 20 |  |
| 12 | t_xxbz_family_member | 小小暴走系统 | 19 |  |
| 13 | 机构自助用户表 | 业务管理系统 | 18 |  |
| 14 | 公众号用户表 | CRM客户关系管理系统 | 18 |  |
| 15 | 机构运营用户表 | 业务管理系统 | 16 |  |
| 16 | user_merge_info | CAS认证系统 | 15 |  |
| 17 | 活动-用户表 | HORN系统 | 14 |  |
| 18 | boss用户表 | HORN系统 | 14 |  |
| 19 | 用户分组规则 | 联劝CRM系统 | 13 |  |
| 20 | 用户表单信息表 | HORN系统 | 13 |  |
| 21 | 外部用户表 | HORN系统 | 12 |  |
| 22 | t_egg_user_team | EGG系统 | 11 |  |
| 23 | t_egg_user_ranking | EGG系统 | 11 |  |
| 24 | t_egg_badge_user | EGG系统 | 11 |  |
| 25 | 用户分组规则 | CRM客户关系管理系统 | 11 |  |
| 26 | 用户-队伍表 | HORN系统 | 11 |  |
| 27 | 用户志愿者小时数累计表 | HORN系统 | 11 |  |
| 28 | 第三方用户关联表 | HORN系统 | 11 |  |
| 29 | 月度筹款总额与新增用户统计表 | 财务支付系统 | 10 |  |
| 30 | t_egg_team_user_pic_temp | EGG系统 | 10 |  |
| 31 | 月捐签约用户月份统计表 | 统计分析系统 | 10 |  |
| 32 | 用户分组 | CRM客户关系管理系统 | 10 |  |
| 33 | 用户菜单关系表 | CRM客户关系管理系统 | 10 |  |
| 34 | 用户菜单关系表 | HORN系统 | 10 |  |
| 35 | 用户信息收集表 | HORN系统 | 10 |  |
| 36 | 邀请捐用户表 | HORN系统 | 10 |  |
| 37 | 耐克员工用户表 | HORN系统 | 10 |  |
| 38 | 用户分组 | 联劝CRM系统 | 9 |  |
| 39 | t_egg_rank_user | EGG系统 | 9 |  |
| 40 | 月捐签约用户月份信息表 | 统计分析系统 | 9 |  |
| 41 | 账户用户关系表 | CRM客户关系管理系统 | 9 |  |
| 42 | 捐赠人用户轨迹表 | CRM客户关系管理系统 | 9 |  |
| 43 | 月捐用户展示图表 | HORN系统 | 9 |  |
| 44 | 日捐用户展示图表 | HORN系统 | 9 |  |
| 45 | 用户自定义捐赠信息表 | HORN系统 | 9 |  |
| 46 | t_xxbz_user_family | 小小暴走系统 | 9 |  |
| 47 | 用户银行信息表 | 业务管理系统 | 8 |  |
| 48 | 用户角色关系表 | CRM客户关系管理系统 | 8 |  |
| 49 | 用户角色关系表 | HORN系统 | 8 |  |
| 50 | 用户个人展示信息表 | HORN系统 | 8 |  |
| 51 | 用户收藏的项目表 | HORN系统 | 8 |  |
| 52 | 微信公众号关注用户情况表 | 联劝CRM系统 | 7 |  |
| 53 | t_egg_activity_user | EGG系统 | 7 |  |
| 54 | 联络用户组 | CRM客户关系管理系统 | 7 |  |
| 55 | t_egg_wx_personal_user | EGG系统 | 6 |  |
| 56 | 公众用户列表显示顺序 | CRM客户关系管理系统 | 6 |  |
| 57 | 月捐用户数变更记录表 | CRM客户关系管理系统 | 6 |  |
| 58 | 用户密码错误记录 | HORN系统 | 6 |  |
| 59 | 标签用户表 | 业务管理系统 | 5 |  |
| 60 | 注册用户统计表 | 统计分析系统 | 5 |  |
| 61 | 短信发送记录表（用户手机） | HORN系统 | 5 |  |
| 62 | 用户读取记录表 | HORN系统 | 5 |  |
| 63 | 海报用户关联表 | CRM客户关系管理系统 | 4 |  |
| 64 | t_xxbz_activity_user | 小小暴走系统 | 4 |  |
| 65 | 合同到期通知用户表 | 业务管理系统 | 3 |  |
| 66 | 捐赠人用户ID | CRM客户关系管理系统 | 2 |  |
| 67 | 用户手机号ID表 | HORN系统 | 2 |  |
| 68 | user_info_synchro | CAS认证系统 | 2 |  |
| 69 | 月捐用户海报表 | 业务管理系统 | 0 |  |
| 70 | 用户标记表 | 业务管理系统 | 0 |  |
| 71 | 鸡蛋暴走用户表 | 业务管理系统 | 0 |  |
| 72 | user_institution_contrast | 业务管理系统 | 0 |  |

## 主要表结构详情

### 用户表 (HORN系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | userId | varchar | 30 | 否 |  | 用户名 |
| 3 | userName | varchar | 20 | 是 |  | 用户真实姓名 |
| 4 | nickName | varchar | 30 | 否 |  | 用户昵称 |
| 5 | birth | datetime |  | 是 |  | 生日 |
| 6 | identityCard | varchar | 18 | 是 |  | 身份证号 |
| 7 | phone | varchar | 11 | 否 |  | 手机 |
| 8 | email | varchar | 48 | 是 |  | 邮箱 |
| 9 | pictureId | varchar | 30 | 是 |  | 头像GUID |
| 10 | donateAccount | varchar | 32 | 是 |  | 用户捐款账户（永久） |
| 11 | uid | varchar | 16 | 是 |  | 用户ID(对外公开用) |
| 12 | nationality | tinyint | 1 | 是 |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 13 | passport | varchar | 18 | 是 |  | 护照号 |
| 14 | sex | tinyint | 1 | 是 |  | 性别（0：男；1：女；2：性别不详） |
| 15 | nationalityDetail | varchar | 255 | 是 |  | 国籍详细 |
| 16 | creattime | varchar | 24 | 是 |  | 生成时间  YYYY-MM-dd HH:mm:ss.SSS |
| 17 | updatetime | varchar | 24 | 是 |  | 更新时间  YYYY-MM-dd HH:mm:ss.SSS |
| 18 | province | int | 11 | 是 |  | 住址的省或直辖市的地名ID |
| 19 | provinceName | varchar | 100 | 是 |  | 住址的省或直辖市的地名 |
| 20 | city | int | 11 | 是 |  | 住址的市ID |
| 21 | cityName | varchar | 100 | 是 |  | 住址的市 |
| 22 | area | int | 11 | 是 |  | 住址的区ID |
| 23 | areaName | varchar | 100 | 是 |  | 住址的区 |
| 24 | detailAddress | varchar | 255 | 是 |  | 住址的详细地址 |
| 25 | homeAddress | varchar | 255 | 是 |  | 住址 |
| 26 | workUnit | varchar | 255 | 是 |  | 工作单位 |
| 27 | address | varchar | 255 | 是 |  | 地址 |
| 28 | job | int | 11 | 是 |  | 工作 |
| 29 | postCode | varchar | 255 | 是 |  | 邮编 |
| 30 | urgentName | varchar | 255 | 是 |  |  |
| 31 | urgentPhone | varchar | 255 | 是 |  |  |
| 32 | contactEmail | varchar | 48 | 是 |  |  |
| 33 | position | varchar | 255 | 是 |  |  |
| 34 | hukouProvince | int | 11 | 是 |  | 户籍地址的省或直辖市的地名ID |
| 35 | hukouProvinceName | varchar | 100 | 是 |  | 户籍地址的省或直辖市的地名 |
| 36 | hukouCity | int | 11 | 是 |  | 户籍地址的市ID |
| 37 | hukouCityName | varchar | 100 | 是 |  | 户籍地址的市 |
| 38 | hukouAddress | varchar | 255 | 是 |  | 户籍地址的详细地址 |
| 39 | hukou | varchar | 255 | 是 |  | 户籍地址 |
| 40 | degrees | int | 1 | 是 |  | 学历（0：初中；1：高中；2：专科；3：大学本科；4：硕士；5：博士及以上） |
| 41 | tSize | int | 1 | 是 |  | T恤尺码（0：S,1：M,2：L,3：XL,4：XXL） |
| 42 | enterpriseName | varchar | 255 | 是 |  | 俱乐部或企业名称 |
| 43 | contestName | varchar | 255 | 是 |  | 比赛名称（6个月内参加） |
| 44 | event | varchar | 255 | 是 |  | 比赛项目（6个月内参加） |
| 45 | score | varchar | 255 | 是 |  | 比赛成绩（6个月内参加） |
| 46 | runMonth | decimal | 10 | 是 |  | 每个月平均跑量（单位：公里） |
| 47 | hottestYear | int | 3 | 是 |  | 平均一年参与跑步比赛场次 |
| 48 | trip | int | 1 | 是 |  | 到达本次活动地点方式（0：自驾；1：公共交通；2：其他） |
| 49 | contactPhone | varchar | 11 | 是 |  | 联系手机 |
| 50 | weixinCard | varchar | 100 | 是 |  | 微信号（歌路营为爱走一夜活动报名用） |
| 51 | campInfo | varchar | 200 | 是 |  | 特定的个人信息（歌路营为爱走一夜活动报名用，数据之间用“,”分割） |

### user_info (CAS认证系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | userguid | varchar | 32 | 否 |  | 用户编号 |
| 3 | username | varchar | 32 | 否 |  | 用户名 |
| 4 | password | varchar | 64 | 是 |  | 密码 |
| 5 | nickname | varchar | 64 | 否 |  | 用户昵称 |
| 6 | phone | varchar | 11 | 是 |  | 手机 |
| 7 | mail | varchar | 50 | 是 |  | 邮箱 |
| 8 | qq | varchar | 64 | 是 |  | QQ（openid，认证登录用） |
| 9 | qqCode | varchar | 16 | 是 |  | QQ号（个人信息） |
| 10 | weibo | varchar | 64 | 是 |  | 微博（openid，认证登录用） |
| 11 | weiboCode | varchar | 48 | 是 |  | 微博（个人信息） |
| 12 | weixin | varchar | 64 | 是 |  | 微信（网站应用openid，认证登录用） |
| 13 | weixinCode | varchar | 32 | 是 |  | 微信（个人信息） |
| 14 | identitycard | varchar | 18 | 是 |  | 身份证号 |
| 15 | passportno | varchar | 32 | 是 |  | 护照编号 |
| 16 | avatarsuffix | varchar | 32 | 是 |  | 头像（大小：160*160；GridFS存储；） |
| 17 | createtime | DateTime |  |  |  | 创建时间（YYYY/MM/DD hh24:mi:ss） |
| 18 | name | varchar | 64 | 是 |  | 真实姓名 |
| 19 | formername | varchar | 64 | 是 |  | 曾用名 |
| 20 | gender | varchar | 1 | 是 | M | 性别，男：M，女：F，不详：N |
| 21 | birthday | Date |  |  |  | 出生日期（YYYY/MM/DD） |
| 22 | permission | varchar | 255 | 是 |  | 用户隐私权限信息；“字段名：权限”，采用“，”分割；不公开：0；群内公开：1；完全公开：2；手机号码为“”（默认）时，不公开；其余选项为“”（默认）时，群内公开； |
| 23 | account | varchar | 32 | 是 |  | 个人永久捐赠账户 |
| 24 | nationality | int | 2 | 是 |  | 国籍（0：中国大陆；1：港澳台及外籍人士） |
| 25 | uid | varchar | 16 | 是 |  | 个人筹款UID |
| 26 | job | int | 2 | 是 | 1 | 职业信息（0：学生；1：公司职员；2：国企事业单位职员；3：自由职业者；4：公益从业人员；5：政府机关人员；6：媒体；7：其他） |
| 27 | address | varchar | 255 | 是 |  | 通讯地址（详细地址） |
| 28 | postCode | varchar | 16 | 是 |  | 邮政编码 |
| 29 | urgentName | varchar | 64 | 是 |  | 紧急联系人姓名 |
| 30 | urgentPhone | varchar | 20 | 是 |  | 紧急联系人手机 |
| 31 | contactEmail | varchar | 50 | 是 |  | 联系邮箱 |
| 32 | unionid | varchar | 32 | 是 |  | 微信用UNIONID |
| 33 | wxnickname | varchar | 64 | 是 |  | 微信用昵称 |
| 34 | province | varchar | 255 | 是 |  | 省份 微信用 |
| 35 | city | varchar | 255 | 是 |  | 城市 微信用 |
| 36 | country | varchar | 255 | 是 |  | 国家 微信用 |
| 37 | headimgurl | varchar | 255 | 是 |  | 头像URL 微信用 |
| 38 | registerIP | varchar | 20 | 是 |  | 注册IP |
| 39 | loginIP | varchar | 20 | 是 |  | 登录IP |
| 40 | loginTime | DateTime |  |  |  | 登录时间 |
| 41 | logoutTime | DateTime |  |  |  | 退出时间 |
| 42 | originalWeixin | varchar | 64 | 是 |  | 原绑定微信（openid，认证登录用） |
| 43 | originalQq | varchar | 64 | 是 |  | 原绑定QQ（openid，认证登录用） |
| 44 | resetPwdFlag | int | 1 | 是 | 0 | 是否需要重置密码（0：不需要；1：需要） |
| 45 | useWxHeadImg | int | 1 | 是 | 1 | 是否使用微信头像（0：不使用；1：使用） |
| 46 | weixinOpenid | varchar | 64 | 是 |  | 微信（OPENID）公众号openid |
| 47 | subscribe | int | 1 | 是 | 0 | 用户是否订阅联劝公益公众号标识，值为0时，代表此用户没有关注该公众号 1：关注了 |
| 48 | workunits | varchar | 128 | 是 |  | 工作单位 |

### t_egg_user (EGG系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | userId | varchar | 30 | 否 |  | 用户名 |
| 3 | userName | varchar | 20 | 否 |  | 用户真实姓名 |
| 4 | nickName | varchar | 32 | 否 |  | 用户昵称 |
| 5 | birth | datetime |  | 是 |  | 生日 |
| 6 | identityCard | varchar | 18 | 是 |  | 身份证号 |
| 7 | phone | varchar | 11 | 是 |  | 手机 |
| 8 | email | varchar | 48 | 是 |  | 邮箱 |
| 9 | pictureId | varchar | 30 | 是 |  | 头像GUID |
| 10 | donateAccount | varchar | 32 | 是 |  | 用户捐款账户（永久） |
| 11 | uid | varchar | 16 | 否 |  | 用户ID(对外公开用) |
| 12 | nationality | tinyint | 1 | 是 |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 13 | passport | varchar | 18 | 是 |  | 护照号 |
| 14 | sex | tinyint | 1 | 是 |  | 性别（0：男；1女） |
| 15 | job | tinyint | 1 | 是 |  | 职业信息（0：学生；1：公司职员；2：国企事业单位职员；3：自由职业者；4：公益从业人员；5：政府机关人员；6：媒体；7：其他） |
| 16 | address | varchar | 50 | 是 |  | 通讯地址（详细地址） |
| 17 | postCode | varchar | 10 | 是 |  | 邮政编码 |
| 18 | urgentName | varchar | 20 | 是 |  | 紧急联系人姓名 |
| 19 | urgentPhone | varchar | 11 | 是 |  | 紧急联系人手机 |
| 20 | contactEmail | varchar | 48 | 是 |  | 联系邮箱 |
| 21 | workUnit | varchar | 255 | 是 |  | 工作单位 |
| 22 | position | varchar | 255 | 是 |  | 工作职位 |
| 23 | nationalityDetail | varchar | 255 | 是 |  | 国籍详细 |
| 24 | hukouProvince | int | 11 | 是 |  | 户籍地址的省或直辖市的地名ID |
| 25 | hukouCity | int | 11 | 是 |  | 户籍地址的市ID |
| 26 | hukouAddress | varchar | 255 | 是 |  | 户籍地址的详细地址 |
| 27 | hukou | varchar | 255 | 是 |  | 户籍地址 |
| 28 | province | int | 11 | 是 |  | 住址的省或直辖市的地名ID |
| 29 | city | int | 11 | 是 |  | 住址的市ID |
| 30 | detailAddress | varchar | 255 | 是 |  | 住址的详细地址 |
| 31 | homeAddress | varchar | 255 | 是 |  | 住址 |
| 32 | contactPhone | varchar | 11 | 是 |  | 联系手机 |
| 33 | insertTime | datetime |  | 是 | CURRENT_TIMESTAMP | 用户首次登录时间 |
| 34 | weixinOpenid | varchar | 255 | 是 |  | 微信公众号的openid |
| 35 | subscribe | int | 4 | 否 | 0 | 0:没有关注 1：已关注 |
| 36 | unionid | varchar | 255 | 是 |  | unionid |

### 用户表 (CRM客户关系管理系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | userName | varchar | 32 | 否 |  | 用户名 |
| 3 | name | varchar | 64 |  |  | 真实姓名 |
| 4 | namePinyin | varchar | 64 |  |  | 姓名拼音 |
| 5 | sex | int | 1 |  | 0 | 性别(0:未知;1:男;2:女) |
| 6 | birthday | Date |  |  |  | 出生日期（YYYY/MM/DD） |
| 7 | phone | varchar | 64 |  |  | 手机 |
| 8 | mail | varchar | 64 |  |  | 邮箱 |
| 9 | nickname | varchar | 64 |  |  | 用户昵称 |
| 10 | institutionId | int | 11 | 否 |  | 组织机构id |
| 11 | deptId | int | 11 |  |  | 部门id |
| 12 | position | varchar | 32 |  |  | 职位 |
| 13 | qq | varchar | 64 |  |  | QQ（openid，认证登录用） |
| 14 | qqCode | varchar | 16 |  |  | QQ号（个人信息） |
| 15 | weibo | varchar | 64 |  |  | 微博（openid，认证登录用） |
| 16 | weiboCode | varchar | 48 |  |  | 微博（个人信息） |
| 17 | weixin | varchar | 64 |  |  | 微信（openid，认证登录用） |
| 18 | weixinCode | varchar | 32 |  |  | 微信（个人信息） |
| 19 | unionid | varchar | 32 |  |  | 微信用UNIONID |
| 20 | province | varchar | 16 |  |  | 省份 微信用 |
| 21 | city | varchar | 32 |  |  | 城市 微信用 |
| 22 | country | varchar | 16 |  |  | 国家 微信用 |
| 23 | headimgurl | varchar | 255 |  |  | 头像URL 微信用 |
| 24 | avatar | int | 11 |  |  | 头像id |
| 25 | createTime | DateTime | 0 | 否 |  | 创建时间 |
| 26 | creator | varchar | 32 |  |  | 创建者用户名（系统账户时为空，其余不为空） |
| 27 | creatorAccount | varchar | 32 | 否 |  | 创建者账户名 |
| 28 | updateTime | DateTime | 0 |  |  | 更新时间 |
| 29 | updater | varchar | 32 |  |  | 更新者用户名（系统账户时为空，其余不为空） |
| 30 | updaterAccount | varchar | 32 |  |  | 更新者账户名 |

### t_xxbz_user (小小暴走系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | userId | varchar | 30 | 是 |  | 用户名 |
| 3 | userName | varchar | 20 | 是 |  | 用户真实姓名 |
| 4 | nickName | varchar | 30 | 是 |  | 用户昵称 |
| 5 | birth | datetime |  | 是 |  | 生日 |
| 6 | identityCard | varchar | 18 | 是 |  | 身份证号/台胞证 |
| 7 | phone | varchar | 11 | 是 |  | 手机 |
| 8 | email | varchar | 48 | 是 |  | 邮箱 |
| 9 | pictureId | varchar | 30 | 是 |  | 头像GUID |
| 10 | donateAccount | varchar | 32 | 是 |  | 用户捐款账户（永久） |
| 11 | uid | varchar | 16 | 否 |  | 用户ID(对外公开用) |
| 12 | nationality | tinyint | 1 | 是 |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 13 | passport | varchar | 18 | 是 |  | 护照号 |
| 14 | sex | tinyint | 1 | 是 |  | 性别（0：男；1女） |
| 15 | contactEmail | varchar | 48 | 是 |  | 联系邮箱 |
| 16 | workUnit | varchar | 255 | 是 |  | 工作单位 |
| 17 | position | varchar | 255 | 是 |  | 工作职位 |
| 18 | nationalityDetail | varchar | 255 | 是 |  | 国籍详细 |
| 19 | hukouProvince | int | 11 | 是 |  | 户籍地址的省或直辖市的地名ID |
| 20 | hukouProvinceNm | varchar | 32 | 是 |  | 户籍地址的省或直辖市的地名 |
| 21 | hukouCity | int | 11 | 是 |  | 户籍地址的市ID |
| 22 | hukouCityNm | varchar | 32 | 是 |  | 户籍地址的市 |
| 23 | hukouAddress | varchar | 255 | 是 |  | 户籍地址的详细地址 |
| 24 | province | int | 11 | 是 |  | 住址的省或直辖市的地名ID |
| 25 | provinceNm | varchar | 32 | 是 |  | 住址的省或直辖市的地名 |
| 26 | city | int | 11 | 是 |  | 住址的市ID |
| 27 | cityNm | varchar | 32 | 是 |  | 住址的市 |
| 28 | detailAddress | varchar | 255 | 是 |  | 住址的详细地址 |

