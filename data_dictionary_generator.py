#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据字典生成工具
生成标准化的数据字典文档
"""

import json
import os
from pathlib import Path

class DataDictionaryGenerator:
    def __init__(self):
        self.output_dir = Path('数据字典文档')
        
        # 业务模块映射
        self.business_modules = {
            '用户管理': ['user', 'member', '用户', '会员'],
            '财务管理': ['account', 'transaction', 'payment', 'invoice', '账户', '交易', '支付', '发票'],
            '捐赠管理': ['donation', 'donate', 'fund', '捐赠', '捐款', '基金'],
            '项目管理': ['project', 'activity', '项目', '活动'],
            '订单管理': ['order', '订单'],
            '证书管理': ['certificate', '证书'],
            '统计分析': ['statistics', 'stat', 'report', '统计', '报表']
        }
        
        # 系统模块映射
        self.system_modules = {
            'cello': '财务支付系统',
            'crm': 'CRM客户关系管理系统',
            'lqscrm': '联劝CRM系统',
            'xxbz': '小小包子系统',
            'statistics': '统计分析系统',
            'banyan': '业务管理系统',
            'cas': 'CAS认证系统',
            'horn': 'HORN系统',
            'egg': 'EGG系统',
            'taxus': 'TAXUS系统'
        }
    
    def load_analysis_data(self):
        """加载分析数据"""
        with open('comprehensive_database_analysis.json', 'r', encoding='utf-8') as f:
            db_analysis = json.load(f)
        
        with open('relationship_analysis_report.json', 'r', encoding='utf-8') as f:
            relationship_analysis = json.load(f)
        
        return db_analysis, relationship_analysis
    
    def generate_system_documentation(self, system_key, system_info):
        """生成单个系统的文档"""
        system_name = system_info['system_name']
        
        # 创建系统文档内容
        content = f"""# {system_name} 数据字典

## 系统概述

**系统名称**: {system_name}  
**系统标识**: {system_key}  
**文件来源**: {system_info['file_name']}.xls  
**工作表总数**: {system_info['total_sheets']}  
**有效表数量**: {system_info['summary']['tables_with_structure']}  
**字段总数**: {system_info['summary']['total_fields']}  

## 表结构清单

"""
        
        # 按字段数量排序表
        tables_sorted = []
        for table_name, table_info in system_info['tables'].items():
            if table_info.get('has_structure', False):
                tables_sorted.append((table_name, table_info))
        
        tables_sorted.sort(key=lambda x: x[1]['field_count'], reverse=True)
        
        # 生成表清单
        content += "| 序号 | 表名 | 字段数 | 说明 |\n"
        content += "|------|------|--------|------|\n"
        
        for i, (table_name, table_info) in enumerate(tables_sorted, 1):
            description = table_info.get('description', '').replace('\n', ' ')[:50]
            content += f"| {i} | {table_name} | {table_info['field_count']} | {description} |\n"
        
        # 生成主要表的详细结构
        content += "\n## 主要表结构详情\n\n"
        
        main_tables = [t for t in tables_sorted if t[1]['field_count'] >= 10][:10]  # 前10个主要表
        
        for table_name, table_info in main_tables:
            content += f"### {table_name}\n\n"
            
            if table_info['fields']:
                content += "| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |\n"
                content += "|------|--------|----------|------|--------|--------|------|\n"
                
                for i, field in enumerate(table_info['fields'], 1):
                    field_name = field.get('name', '')
                    field_type = field.get('type', '')
                    field_length = field.get('length', '')
                    field_nullable = field.get('nullable', '')
                    field_default = field.get('default', '')
                    field_comment = field.get('comment', '').replace('\n', ' ')[:100]
                    
                    content += f"| {i} | {field_name} | {field_type} | {field_length} | {field_nullable} | {field_default} | {field_comment} |\n"
            
            content += "\n"
        
        return content
    
    def generate_business_module_documentation(self, module_name, db_analysis, relationship_analysis):
        """生成业务模块文档"""
        keywords = self.business_modules.get(module_name, [])
        
        # 收集相关表
        related_tables = []
        for system_key, system_info in db_analysis.items():
            if 'error' in system_info:
                continue
            
            for table_name, table_info in system_info['tables'].items():
                if not table_info.get('has_structure', False):
                    continue
                
                table_name_lower = table_name.lower()
                if any(keyword in table_name_lower for keyword in keywords):
                    related_tables.append({
                        'system': system_key,
                        'system_name': system_info['system_name'],
                        'table_name': table_name,
                        'field_count': table_info['field_count'],
                        'table_info': table_info
                    })
        
        # 按字段数排序
        related_tables.sort(key=lambda x: x['field_count'], reverse=True)
        
        # 生成文档内容
        content = f"""# {module_name}模块 数据字典

## 模块概述

**业务模块**: {module_name}  
**关键词**: {', '.join(keywords)}  
**相关表数量**: {len(related_tables)}  
**涉及系统**: {len(set(t['system'] for t in related_tables))}个  

## 相关表清单

"""
        
        if related_tables:
            content += "| 序号 | 表名 | 所属系统 | 字段数 | 说明 |\n"
            content += "|------|------|----------|--------|------|\n"
            
            for i, table in enumerate(related_tables, 1):
                description = table['table_info'].get('description', '').replace('\n', ' ')[:50]
                content += f"| {i} | {table['table_name']} | {table['system_name']} | {table['field_count']} | {description} |\n"
            
            # 详细表结构（前5个主要表）
            content += f"\n## 主要表结构详情\n\n"
            
            for table in related_tables[:5]:
                table_info = table['table_info']
                content += f"### {table['table_name']} ({table['system_name']})\n\n"
                
                if table_info['fields']:
                    content += "| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |\n"
                    content += "|------|--------|----------|------|--------|--------|------|\n"
                    
                    for i, field in enumerate(table_info['fields'], 1):
                        field_name = field.get('name', '')
                        field_type = field.get('type', '')
                        field_length = field.get('length', '')
                        field_nullable = field.get('nullable', '')
                        field_default = field.get('default', '')
                        field_comment = field.get('comment', '').replace('\n', ' ')[:100]
                        
                        content += f"| {i} | {field_name} | {field_type} | {field_length} | {field_nullable} | {field_default} | {field_comment} |\n"
                
                content += "\n"
        else:
            content += "暂未发现相关表。\n"
        
        return content
    
    def generate_all_documentation(self):
        """生成所有文档"""
        print("正在生成数据字典文档...")
        
        # 加载数据
        db_analysis, relationship_analysis = self.load_analysis_data()
        
        # 1. 生成系统模块文档
        print("\n生成系统模块文档...")
        for system_key, system_info in db_analysis.items():
            if 'error' in system_info:
                continue
            
            system_name = system_info['system_name']
            print(f"  生成 {system_name} 文档...")
            
            content = self.generate_system_documentation(system_key, system_info)
            
            # 保存文档
            output_path = self.output_dir / '03-系统模块' / system_name / f'{system_name}_数据字典.md'
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        # 2. 生成业务模块文档
        print("\n生成业务模块文档...")
        for module_name in self.business_modules.keys():
            print(f"  生成 {module_name} 文档...")
            
            content = self.generate_business_module_documentation(module_name, db_analysis, relationship_analysis)
            
            # 保存文档
            output_path = self.output_dir / '02-业务模块' / module_name / f'{module_name}_数据字典.md'
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        print("\n✅ 数据字典文档生成完成！")

if __name__ == "__main__":
    generator = DataDictionaryGenerator()
    generator.generate_all_documentation()
