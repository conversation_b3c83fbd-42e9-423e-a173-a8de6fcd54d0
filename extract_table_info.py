#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取数据库表结构信息的工具
专门用于分析上海联劝公益基金会的数据库设计文档
"""

import pandas as pd
import os
import json
from pathlib import Path

def extract_table_structure(file_path, sheet_name):
    """提取单个工作表的表结构信息"""
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        
        # 查找表结构的开始位置（通常包含"字段名"、"数据类型"等关键词）
        table_info = {
            'table_name': sheet_name,
            'fields': [],
            'description': '',
            'indexes': [],
            'constraints': []
        }
        
        # 扫描前20行寻找表结构定义
        for i in range(min(20, len(df))):
            row = df.iloc[i]
            row_str = ' '.join([str(cell) for cell in row if pd.notna(cell)])
            
            # 查找表描述
            if '表说明' in row_str or '表描述' in row_str or '用途' in row_str:
                table_info['description'] = row_str
            
            # 查找字段定义开始的行
            if any(keyword in row_str for keyword in ['字段名', '列名', 'Field', 'Column']):
                # 找到字段定义的标题行，开始解析字段
                header_row = i
                field_name_col = None
                data_type_col = None
                comment_col = None
                
                # 确定各列的位置
                for j, cell in enumerate(row):
                    if pd.notna(cell):
                        cell_str = str(cell).strip()
                        if '字段名' in cell_str or 'Field' in cell_str:
                            field_name_col = j
                        elif '数据类型' in cell_str or 'Type' in cell_str or '类型' in cell_str:
                            data_type_col = j
                        elif '说明' in cell_str or '注释' in cell_str or 'Comment' in cell_str:
                            comment_col = j
                
                # 解析字段信息
                if field_name_col is not None:
                    for k in range(header_row + 1, min(header_row + 50, len(df))):
                        field_row = df.iloc[k]
                        field_name = field_row.iloc[field_name_col] if field_name_col < len(field_row) else None
                        
                        if pd.notna(field_name) and str(field_name).strip():
                            field_info = {
                                'name': str(field_name).strip(),
                                'type': '',
                                'comment': ''
                            }
                            
                            if data_type_col is not None and data_type_col < len(field_row):
                                data_type = field_row.iloc[data_type_col]
                                if pd.notna(data_type):
                                    field_info['type'] = str(data_type).strip()
                            
                            if comment_col is not None and comment_col < len(field_row):
                                comment = field_row.iloc[comment_col]
                                if pd.notna(comment):
                                    field_info['comment'] = str(comment).strip()
                            
                            table_info['fields'].append(field_info)
                        else:
                            # 如果字段名为空，可能已经到了表结构的末尾
                            break
                
                break
        
        return table_info
        
    except Exception as e:
        return {'table_name': sheet_name, 'error': str(e)}

def analyze_database_design():
    """分析所有数据库设计文档"""
    current_dir = Path('.')
    excel_files = list(current_dir.glob('*.xls*'))
    
    # 系统模块映射
    system_modules = {
        'banyan': '业务管理系统',
        'cas': 'CAS认证系统', 
        'cello': '财务支付系统',
        'crm': 'CRM客户关系管理系统',
        'egg': 'EGG系统',
        'horn': 'HORN系统',
        'lqscrm': '联劝CRM系统',
        'statistics': '统计分析系统',
        'taxus': 'TAXUS系统',
        'xxbz': '小小包子系统'
    }
    
    all_systems = {}
    
    for excel_file in excel_files:
        file_name = excel_file.stem
        system_name = file_name.replace('_dataBaseModelDesign', '')
        system_desc = system_modules.get(system_name, system_name)
        
        print(f"\n正在分析系统: {system_desc} ({file_name})")
        
        try:
            excel_file_obj = pd.ExcelFile(excel_file)
            sheet_names = excel_file_obj.sheet_names
            
            system_info = {
                'system_name': system_desc,
                'file_name': file_name,
                'table_count': len(sheet_names),
                'tables': {}
            }
            
            # 分析每个工作表（表）
            for sheet_name in sheet_names[:10]:  # 限制前10个表进行详细分析
                print(f"  分析表: {sheet_name}")
                table_info = extract_table_structure(excel_file, sheet_name)
                system_info['tables'][sheet_name] = table_info
            
            all_systems[system_name] = system_info
            
        except Exception as e:
            print(f"错误: 无法分析文件 {excel_file}: {e}")
            all_systems[system_name] = {
                'system_name': system_desc,
                'file_name': file_name,
                'error': str(e)
            }
    
    # 保存结果
    with open('database_structure_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(all_systems, f, ensure_ascii=False, indent=2)
    
    # 生成摘要报告
    print("\n=== 数据库结构分析摘要 ===")
    for system_key, system_info in all_systems.items():
        if 'error' in system_info:
            print(f"{system_info['system_name']}: 错误 - {system_info['error']}")
        else:
            print(f"{system_info['system_name']}: {system_info['table_count']} 个表")
            for table_name, table_info in system_info['tables'].items():
                if 'error' in table_info:
                    print(f"  - {table_name}: 解析错误")
                else:
                    field_count = len(table_info['fields'])
                    print(f"  - {table_name}: {field_count} 个字段")

if __name__ == "__main__":
    analyze_database_design()
