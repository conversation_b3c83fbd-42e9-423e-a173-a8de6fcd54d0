#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找字段说明列的工具
"""

import pandas as pd

def find_comment_column(file_path, sheet_name):
    """查找字段说明列"""
    print(f"查找表 {sheet_name} 的字段说明列")
    print("=" * 80)
    
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        
        # 查找标题行
        for i in range(min(10, len(df))):
            row = df.iloc[i]
            print(f"\n行 {i+1}:")
            for j in range(len(row)):
                cell = row.iloc[j]
                if pd.notna(cell):
                    cell_str = str(cell).strip()
                    if cell_str:
                        print(f"  列{j+1:2d}: {cell_str}")
        
        print("\n" + "=" * 80)
        
        # 查找第一个数据行的所有列
        data_row_idx = None
        for i in range(len(df)):
            row = df.iloc[i]
            if len(row) > 2 and pd.notna(row.iloc[2]) and str(row.iloc[2]).strip() == "1":
                data_row_idx = i
                break
        
        if data_row_idx:
            print(f"\n第一个数据行 (行{data_row_idx+1}) 的所有列:")
            row = df.iloc[data_row_idx]
            for j in range(len(row)):
                cell = row.iloc[j]
                if pd.notna(cell):
                    cell_str = str(cell).strip()
                    if cell_str:
                        print(f"  列{j+1:2d}: {cell_str}")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    find_comment_column("xxbz_dataBaseModelDesign.xls", "t_xxbz_user")
