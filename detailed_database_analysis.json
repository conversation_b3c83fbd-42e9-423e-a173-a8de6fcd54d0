{"cello": {"system_key": "cello", "system_name": "财务支付系统", "file_name": "cello_dataBaseModelDesign", "table_count": 116, "tables": {"项目基本信息": {"table_name": "项目基本信息", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "目录": {"table_name": "目录", "chinese_name": "中文名", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历": {"table_name": "更新履历", "chinese_name": "更新类别", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历(视图)": {"table_name": "更新履历(视图)", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历（存储过程）": {"table_name": "更新履历（存储过程）", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "存储过程说明": {"table_name": "存储过程说明", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "配置表": {"table_name": "配置表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "账户表": {"table_name": "账户表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "帐户类型表": {"table_name": "帐户类型表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "帐户状态表": {"table_name": "帐户状态表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "帐户合并表": {"table_name": "帐户合并表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "自动转账规则表": {"table_name": "自动转账规则表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "实体表": {"table_name": "实体表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "实体类型表": {"table_name": "实体类型表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "交易代码表": {"table_name": "交易代码表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "交易流水表": {"table_name": "交易流水表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "明细流水表": {"table_name": "明细流水表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "支付类型年月统计表": {"table_name": "支付类型年月统计表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "支付操作日志表": {"table_name": "支付操作日志表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公募支付配置表": {"table_name": "公募支付配置表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}}}, "banyan": {"system_key": "banyan", "system_name": "业务管理系统", "file_name": "banyan_dataBaseModelDesign", "table_count": 287, "tables": {"项目基本信息": {"table_name": "项目基本信息", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "表目录": {"table_name": "表目录", "chinese_name": "中文名", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "视图目录": {"table_name": "视图目录", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "函数目录": {"table_name": "函数目录", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "事件目录": {"table_name": "事件目录", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历": {"table_name": "更新履历", "chinese_name": "更新类别", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历(视图)": {"table_name": "更新履历(视图)", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "证书模板表2": {"table_name": "证书模板表2", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "移动文件临时表": {"table_name": "移动文件临时表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "管理员帐号表": {"table_name": "管理员帐号表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "活动商品关系表": {"table_name": "活动商品关系表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "首页轮播图管理表": {"table_name": "首页轮播图管理表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "活动项目标签表": {"table_name": "活动项目标签表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公益活动表": {"table_name": "公益活动表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "活动报销清单表": {"table_name": "活动报销清单表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公益活动详情表": {"table_name": "公益活动详情表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "活动报销申请表": {"table_name": "活动报销申请表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "活动队伍关联表": {"table_name": "活动队伍关联表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "行政管理费比例表": {"table_name": "行政管理费比例表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "行政预算表": {"table_name": "行政预算表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}}}, "lqscrm": {"system_key": "lqscrm", "system_name": "联劝CRM系统", "file_name": "lqscrm_dataBaseModelDesign", "table_count": 58, "tables": {"项目基本信息": {"table_name": "项目基本信息", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "目录": {"table_name": "目录", "chinese_name": "中文名", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历": {"table_name": "更新履历", "chinese_name": "更新类别", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历(视图)": {"table_name": "更新履历(视图)", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历（存储过程）": {"table_name": "更新履历（存储过程）", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信第三方平台配置表": {"table_name": "微信第三方平台配置表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信第三方平台调用凭据表": {"table_name": "微信第三方平台调用凭据表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号授权信息表": {"table_name": "公众号授权信息表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信公众账号表": {"table_name": "微信公众账号表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信公众账号口令表": {"table_name": "微信公众账号口令表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信公众账号信息表 ": {"table_name": "微信公众账号信息表 ", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信推送接收人表 ": {"table_name": "微信推送接收人表 ", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信接收人标签表 ": {"table_name": "微信接收人标签表 ", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信标签表": {"table_name": "微信标签表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信公众账号授权表": {"table_name": "微信公众账号授权表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号基本信息表": {"table_name": "公众号基本信息表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号用户消息表": {"table_name": "公众号用户消息表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号素材表": {"table_name": "公众号素材表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号行业信息表": {"table_name": "公众号行业信息表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号关联行业信息表": {"table_name": "公众号关联行业信息表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}}}, "egg": {"system_key": "egg", "system_name": "EGG系统", "file_name": "egg_dataBaseModelDesign", "table_count": 78, "tables": {"更新履历": {"table_name": "更新履历", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "视图更新履历": {"table_name": "视图更新履历", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "文档列表": {"table_name": "文档列表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "视图列表": {"table_name": "视图列表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_user": {"table_name": "t_egg_user", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_user_team": {"table_name": "t_egg_user_team", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_activity_user": {"table_name": "t_egg_activity_user", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_team": {"table_name": "t_egg_team", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_activity_group_team": {"table_name": "t_egg_activity_group_team", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_activity": {"table_name": "t_egg_activity", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_group": {"table_name": "t_egg_group", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_team_group": {"table_name": "t_egg_team_group", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_teamtype": {"table_name": "t_egg_teamtype", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_activity_teamtype": {"table_name": "t_egg_activity_teamtype", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_activityterm": {"table_name": "t_egg_activityterm", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_activity_activityterm": {"table_name": "t_egg_activity_activityterm", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_activityterm_apply": {"table_name": "t_egg_activityterm_apply", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_punchset": {"table_name": "t_egg_punchset", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_achievem": {"table_name": "t_egg_achievem", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_egg_achievem_original": {"table_name": "t_egg_achievem_original", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}}}, "statistics": {"system_key": "statistics", "system_name": "统计分析系统", "file_name": "statistics_dataBaseModelDesign", "table_count": 30, "tables": {"项目基本信息": {"table_name": "项目基本信息", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "目录": {"table_name": "目录", "chinese_name": "中文名", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历": {"table_name": "更新履历", "chinese_name": "更新类别", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历(视图)": {"table_name": "更新履历(视图)", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "统计配置表": {"table_name": "统计配置表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "注册用户统计表": {"table_name": "注册用户统计表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "实体类型表": {"table_name": "实体类型表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "支付类型表": {"table_name": "支付类型表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "联劝网筹款产品表": {"table_name": "联劝网筹款产品表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "每日捐赠统计表": {"table_name": "每日捐赠统计表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "每月捐赠统计表": {"table_name": "每月捐赠统计表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "月度24小时统计表": {"table_name": "月度24小时统计表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "发票统计表": {"table_name": "发票统计表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "月捐人统计表": {"table_name": "月捐人统计表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "月捐每日统计表": {"table_name": "月捐每日统计表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "月捐月度统计表": {"table_name": "月捐月度统计表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "每月捐赠统计": {"table_name": "每月捐赠统计", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "爱扑满日统计表": {"table_name": "爱扑满日统计表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "月捐邀请日统计表": {"table_name": "月捐邀请日统计表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "新增实名用户统计表": {"table_name": "新增实名用户统计表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}}}, "taxus": {"system_key": "taxus", "system_name": "TAXUS系统", "file_name": "taxus_dataBaseModelDesign", "table_count": 143, "tables": {"更新履历": {"table_name": "更新履历", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "数据库表列表": {"table_name": "数据库表列表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "视图存储过程事件": {"table_name": "视图存储过程事件", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "账户表": {"table_name": "账户表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "明细流水表": {"table_name": "明细流水表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "帐户状态表": {"table_name": "帐户状态表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "帐户类型表": {"table_name": "帐户类型表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "平账补充收入记录表": {"table_name": "平账补充收入记录表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "行政管理费提取比例表": {"table_name": "行政管理费提取比例表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "行政管理费后支付订单的明细流水表": {"table_name": "行政管理费后支付订单的明细流水表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "行政管理费后支付订单表 ": {"table_name": "行政管理费后支付订单表 ", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "支付宝对账单表": {"table_name": "支付宝对账单表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "资金转移事件序列表": {"table_name": "资金转移事件序列表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "拨付表": {"table_name": "拨付表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "对账状态表": {"table_name": "对账状态表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "自动对账履历表": {"table_name": "自动对账履历表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "财付通对账单表": {"table_name": "财付通对账单表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "资金结转记录表": {"table_name": "资金结转记录表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "捐赠人ID关联表": {"table_name": "捐赠人ID关联表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "捐赠人变更记录表": {"table_name": "捐赠人变更记录表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}}}, "crm": {"system_key": "crm", "system_name": "CRM客户关系管理系统", "file_name": "crm_dataBaseModelDesign", "table_count": 148, "tables": {"项目基本信息": {"table_name": "项目基本信息", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "目录": {"table_name": "目录", "chinese_name": "中文名", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历": {"table_name": "更新履历", "chinese_name": "更新类别", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历(视图)": {"table_name": "更新履历(视图)", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历（存储过程）": {"table_name": "更新履历（存储过程）", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信第三方平台配置表": {"table_name": "微信第三方平台配置表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信第三方平台调用凭据表": {"table_name": "微信第三方平台调用凭据表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号授权信息表": {"table_name": "公众号授权信息表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "机构微信公众号关联表": {"table_name": "机构微信公众号关联表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信公众账号表": {"table_name": "微信公众账号表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "微信公众账号授权表": {"table_name": "微信公众账号授权表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号基本信息表": {"table_name": "公众号基本信息表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号用户表": {"table_name": "公众号用户表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号用户消息表": {"table_name": "公众号用户消息表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号素材表": {"table_name": "公众号素材表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号行业信息表": {"table_name": "公众号行业信息表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号关联行业信息表": {"table_name": "公众号关联行业信息表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号行业模板表": {"table_name": "公众号行业模板表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号模板关联表": {"table_name": "公众号模板关联表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "公众号消息模板表": {"table_name": "公众号消息模板表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}}}, "horn": {"system_key": "horn", "system_name": "HORN系统", "file_name": "horn_dataBaseModelDesign", "table_count": 285, "tables": {"项目基本信息": {"table_name": "项目基本信息", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "目录": {"table_name": "目录", "chinese_name": "中文名", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历": {"table_name": "更新履历", "chinese_name": "更新类别", "description": "", "fields": [{"name": "修改字段remark类型", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段lastEditStep", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加单笔捐信息收集数据", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段isNeedTeamIntroduce", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段payAmount", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段remark", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段updateTime", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据项【CAS用户信息批量注册接口（新）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据项【xxbz 用项目URL】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段 isMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段 isMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改数据项【评分系统-评分基本分】和【评分系统-初始评分】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段openId", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据项【codeCenter】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段 objName", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加一条记录【36】留言管理", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段 closer，underlineTime，reason", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段 posterWord1、posterWord2、posterPicId", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段posterWord1、posterWord2,添加字段posterWord", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据项【获取敏感词库】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段picWidth，picHeight，picStatusId，picData", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段progressId，type", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段jointDonationLabel、inviteDonationLabel", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段mobileUrl", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段userId", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录：待办事项（id:8）\n添加记录：活动、项目、月捐编辑tongzhi审核通知模板(6个,id:19～24)", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段formFlag\n添加字段signupForm", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段mobileUrl", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段entryType、matchAll、updateTime、logo\n添加字段picName", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加列表项reviewOrderInterval", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加列表项orderMessageReview、orderAnswerReview", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加type值参数6：月捐单笔捐", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段numStartTime、numEndTime、numDetailUrl", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段numStartTime、numEndTime、numDetailUrl", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段numStartTime、numEndTime、numDetailUrl", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段numStartTime、numEndTime、numDetailUrl", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段numStartTime、numEndTime、numDetailUrl", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段numStartTime、numEndTime、numDetailUrl", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录(通知模板，id：25,26)", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录(id：85～91)", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【projectFundStart】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段toolsOperateFlg", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段toolsOperateFlg", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加列表项toolsReview", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【projectFundStart】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段picId，phonePicId，删除字段picData，picDataPhone", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "报名表单默认模板样式修改content（No.1和No.9的空白模板）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录（key：wwwboss）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【signupForm】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【entryType】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【signupForm】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段weixin、qq、weibo、douyin、linkedin、ip", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加默认配置项（institutionId=0）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【orderNo】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录：资金系统接口URL（旧版） taxusold  http://localhost:8080/taxus/taxus/  0", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【ifCas】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【status】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【status】，【charityId】允许为空", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "机构年统计表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【posterWord1】、【posterWord2】、【posterWord3】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据id为27,28（邮件模板和通知模板一样）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段targetStr1、targetStr2、targetStr3、targetflg", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【lastProgressTime】最后一次反馈通过审核的时间（日期）,【remindProgressTime】反馈提醒的时间（日期）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表，反馈提醒记录表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表，审核记录表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加列数据【solrDataLastUpdId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除name=展示图、机构介绍、资讯管理的数据，修改name=机构主页", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【area】、【areaName】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【style】系列类型（0：活动；1：项目；2：月捐）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【actTime】、【address】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加联劝3个U月捐的月捐编号配置【lqUContract】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "改为新版本证书，添加6个字段", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段type、原字段fontStyle改为varchar类型，添加默认值“0”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【totalDonatorNum】、【ystdDonatorNum】、【lastUpdTime】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据项【institutionOperateManage】、【dataAnalysisManage】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "变更地址字段", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加定制捐赠证书菜单", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段【startTime】、【endTime】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段【score】、【sortFlag】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段【score】、【sortFlag】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【openInvoice】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加个人中心邮件发送方配置【ivySenderEmail】、【ivySender】、【ivyReplyEmail】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除code=upgradenotice的记录，设置联劝网管理下的菜单num", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "字段【releaseContext】由text类型改为varchar(1000),添加字段【platform】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加code=platformUpgradeNotice、code=commonProblem的记录", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加ckey=amount的记录", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fixShow】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加name=首页排序调整的记录", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加ckey=useHtml的记录", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【modeId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【videoSize】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【content2】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isOss】、【ossUrl】、【deltid】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isOss】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isOss】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isOss】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加触发器【trigger_content】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加ckey=activityOss、ckey=projectOss、ckey=contractOss、\nckey=endpoint、ckey=accessKeyId、ckey=accessKeySecret、ckey=bucketName的记录", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fundGoalsAlias】、【fundGoalsFlag】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加ckey=ossUrl、ckey=speedType、ckey=cdnUrl的记录", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【postCode】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据【posrUrlPc】、【postUrlPhone】，义卖快递110查询用", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据【casSpeedType】、【casBucketName】、【casOssMode】、【ossCasUrl】、【cdnCasUrl】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isNeedCom】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isNeedCom】、【logisticalAccount】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段【type】、【isFreeShipping】，添加字段【specification】、【shelfTime】、【offShelfTime】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【holderId】、【holderType】、【shelfTime】、【offShelfTime】，删除字段【activityId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【holderId】、【holderType】、【shelfTime】、【offShelfTime】，删除字段【activityId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【institutionId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【holderId】、【holderType】，删除字段【activityId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【holderId】、【holderType】，删除字段【activityId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【holderId】、【holderType】，删除字段【activityId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【holderId】、【holderType】，删除字段【activityId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【holderId】、【holderType】，删除字段【activityId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【holderId】、【holderType】，删除字段【activityId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表【爱扑满模板】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【specification】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isNeedCom】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isNeedCom】、【logisticalAccount】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录【线下劝募cookie domain 】ckey:【extensionCookieDomain】cvalue:【lianquan.org】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改字段content的内容", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段holderType", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段isRisk", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字典【isUsed】、【start】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isOss1】、【ossUrl1】、【delflg1】、【isOss2】、【ossUrl2】、【delflg2】\n添加触发器【trigger_u_activity_bf】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isOss1】、【ossUrl1】、【delflg1】、【isOss2】、【ossUrl2】、【delflg2】\n添加触发器【trigger_u_contract_bf】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isOss1】、【ossUrl1】、【delflg1】、【isOss2】、【ossUrl2】、【delflg2】\n添加触发器【trigger_u_ful_project_bf】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isOss1】、【ossUrl1】、【delflg1】、【isOss2】、【ossUrl2】、【delflg2】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加触发器【trigger_uploadTime】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据项（qq登录appid、qq登录跳转url、weixin登录appid、weixin登录跳转url、weixin登录密钥、\nqq登录密钥、授权中心、CAS用户信息合并接口、报名捐赠系统weixin登录跳转url、个人中心weixin登录跳转url）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "\"机构统计\"菜单添加", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "邮件任务表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "邮件任务联络表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "mailId字段长度改为32", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加mailTemplate", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加“机构筹款月统计邮件模板”，apid为“MTE00000000000000000001”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "插入两行数据：notificationsCode=fpsqtxdx、notificationsCode=fpsqtxyj", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "插入一行数据：ckey=sendMsgAndMail", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "插入【首页】，其他pid是null的num值为0的改为1", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加菜单数据【月捐统计】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加菜单数据【月捐统计】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【insId】、【ifImport】、【createflag】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加行key=\"createCertificate\"", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加行code=\"finishMatchReview\"", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "将name=定制捐赠证书 修改为“捐赠证书模板一览”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改触发器trigger_u_activity_af", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改触发器trigger_u_ful_project_af", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改触发器trigger_u_contract_af", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【flag】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【reviewDetail】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录contractCertificateReview", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "公募机构增加一条记录，menuId=contractCertificateReview在menu表的id", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改月捐证书模板", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录【yjzstjshmb】、【yjzsshmb】、【yjzsqrmb】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录【ContractCertReview】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【problemId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【platform】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【status】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加日捐相关表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【name】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【invoiceNeed】，处理历史数据，没有月捐开票信息都设为0", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "新加记录以及更新原本的记录，直接拷贝20即可", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fundMatch】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fundraisingMoney】、【fundraisingCount】、【fundMatch】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fundraisingMoney】、【fundraisingCount】、【fundMatch】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fundraisingMoney】、【fundraisingCount】、【contractNumber】、【accountContractMoney】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fundraisingMoney】、【fundraisingCount】、【contractNumber】、【accountContractMoney】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fundraisingMoney】、【fundraisingCount】、【fundMatch】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fundraisingCount】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录constantsAutoPjtIds、constantsAutoActIds、constantsAutoCatIds、constantsAutoDailyCatIds、constantsDailyCatIds", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段fundraisingMoney、fundraisingCount", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段fundraisingMoney、fundraisingCount、fundMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段fundraisingMoney、fundraisingCount", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段countMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段countMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、fundMatch、countMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、fundMatch、countMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、accountContractMoney、contractNumber", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、accountContractMoney、contractNumber", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段countMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段countMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段countMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段countMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加一条基础数据ckey=\"network_user\"", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundMatch、countMatch，删除字段fundraisingMoney、fundraisingCount、enterpriseCount", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、fundMatch、countMatch、删除字段enterpriseCount", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、fundMatch、countMatch、删除字段enterpriseCount", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、accountContractMoney、contractNumber", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、accountContractMoney、contractNumber", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、fundMatch、countMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、fundMatch、countMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、accountContractMoney、contractNumber", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、accountContractMoney、contractNumber", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段fundraisingMoney、fundraisingCount、fundMatch、countMatch、删除字段enterpriseCount", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段fundraisingMoney、fundraisingCount，添加字段fundMatch、countMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表,添加列fundraisingFundraising", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录constantsAutoPjtIds、constantsAutoActIds、constantsAutoCatIds、constantsAutoDailyCatIds、constantsDailyCatIds", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段fundraisingMoney、fundraisingCount、fundMatch、enterpriseCount，添加字段countMatch", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段accountSingleMoney", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "资金侧字段默认值改为0（raiseAmount、raiseCount、matchAmount、fundraisingMoney、fundraisingCount、fundMatch、countMatch）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段 posterWord、posterPicId", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段teamTop520Start、teamTop520End", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段certificType、groupNumber、groupId、group<PERSON>ongTime、groupStartTime、groupStartTime、cardHolder、benefitType、benefitCardType、benefitCardFrontId、benefitCardBackId、benefitCardNo、benefitCardHolder、benefitCardLongTime、benefitCardStartTime、benefitCardEndTime、shopName、shopProvence、shopCity、shopPicId、shopEnvId、shopAppId、appIdType、serviceAppId、businessAppId、appIdPicId、appletAppId、appletPicId、applyType、serviceApplyId、businessApplyId、applyPicId、webAuthPicId、webAppId、corpID、corpPicId、specialPicId、adminType、adminName、adminInfoType、adminNumber、weixinOpenid、adminPhone、adminMail", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "新增表t_horn_xh_show\n新增数据：（表：t_zither_menu)    name = 新虹记疫展\n新增数据：（表：t_horn_webconfig）    ckey = xhShowOss", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改增加语句{\nIF  OLD.holderType = 11 THEN\n              \n               update t_horn_donate_story set isOss = 0 where storyId = OLD.apid;\n END IF;\n\n IF  OLD.holderType = 12 THEN\n              \n               update t_horn_xh_show set isOss = 0 where showId = OLD.apid;\n END IF;\n}", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "新增表,t_horn_channel表有一条默认数据，复制20的第一条数据，usageCount改为0", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "增加菜单`资源中心管理`及`子菜单`", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "增加配置`shorturl`", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "新增表t_horn_recommend", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "增加配置“recommendOss”、WX_TEMPLATE_RUL、shorturl", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录【ScrmChargeInfo】，SCRM收入明细表 菜单", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "资源记录修改，t_horn_sort基础数据", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "实际环境上要配置rabbitmq环境（配置信息在harp2019rabbitmq_config.properties文件中）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【effectiveTime】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【stopAccountFlag】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【invoiceType】、【creditCode】、【email】、【creditType】\n取消字段【recipientName】、【recipientAddress】、【postType】的必填", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【invoiceType】、【creditCode】、【email】、【creditType】、【invoiceMethod】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【invoiceElec】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加配置信息【168、169、170、171】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【invoiceInsGuid】、【invoicePrivateKey】、【invoiceSmsTmp】、【invoiceSmsSign】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表结构", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "将数据库中菜单中“发票”改成“票据”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【institutionGuid】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录【新资金接口URL，census，http://localhost:10001】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加【availableTimes】 剩余可发生次数，默认值1", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除事件：event_extend_attribute_count、event_get_and_update_score、event_success_check_over_or_not、event520py、event_success_order", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除触发器：\nt_ads_user触发器trigger_form_sync_af\nt_horn_donate_story_evaluation触发器trigger_donate_story_evaluation_af\nt_horn_activity触发器trigger_u_activity_bf、trigger_u_activity_af\nt_horn_fulcommonweal_project触发器trigger_u_ful_project_bf、trigger_u_ful_project_af\nt_horn_contract触发器trigger_u_contract_bf、trigger_u_contract_af\nt_horn_daily_contract触发器trigger_u_daily_contract_bf、trigger_u_daily_contract_af\nt_horn_file触发器trigger_uploadTime\ntemplate触发器trigger_content\nt_horn_series触发器trigger_u_series_af\nt_ads_activity_user触发器au_trigger_update_target_af_copy\nt_ads_user_team触发器ut_trigger_update_agt_af、ut_trigger_insert_agt_af", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "设置基础数据fromDB=horn，tableName=t_ads_user，startTime=触发器删除时间\n设置基础数据fromDB=horn，tableName=t_horn_donate_story_evaluation，startTime=触发器删除时间\n设置基础数据fromDB=horn，tableName=t_horn_file，startTime=触发器删除时间", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "【invoiceMailSender】、【invoiceMailSendMail】、【invoiceMailReplayMail】、【invoiceMailTmp】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "【donateflag】 是否显示捐赠按钮，默认值0显示", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段autoInvoice（是否设置开票方式）、customPoster（自定义分享海报）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加菜单月度运营报告及其子菜单、月捐分析报告及其子菜单", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "code为sortManager的字段name由“首页排序调整”调整为“首页/列表排序调整”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表，月捐捐赠信息收集自定义配置表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表，月捐捐赠信息收集自定义配置修改表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表，月捐用户自定义捐赠信息表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加range为\"00100000\"的数据，content、designhtml字段做了调整", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段firstOnlineTime", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段invoiceMethod", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "update t_horn_donate_config_definition set itemName = \"称谓\" where itemName = \"性别\"\nupdate t_horn_donate_config_definition_edit set itemName = \"称谓\" where itemName = \"性别\"", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "【logId】允许为空", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表，推广文案表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段detailKey", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段extendAttribute", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【institutionGuid】、【fundGuid】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据列4; t_horn_contract", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据【GET_WX_OPEN_ID】、【WX_SECRET】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "增加字段“createTime、closeTime、closer、closeReason”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "增加字段“contractCode、amount、allamount、status、expiredTime”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加菜单的基础数据", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表结构", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【updateTime】根据时间戳更新", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【updateTime】根据时间戳更新", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据【apmMonitor】、【invitationMonitor】、【relieveInvitationMonitor】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加【apmjzrcyj】、【apmjzrcyj】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加【apmMonitor】、【invitationMonitor】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表结构", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据【tbWelfareUrl】、【tbWelfareAppkey】、【tbWelfareSecret】、【tbWelfareActivityId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表结构", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表结构", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表机构", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表机构", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表机构", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表机构", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据【allApmMonitor】、【allApmMonitor】、【allRelieveInvitationMonitor】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表及基础数据", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据id为125,126,128", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据id为182,183,184,185,186", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【amount】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【amount】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【amount】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【amount】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【amount】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【amount】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fundraisingStartTime】、【fundraisingEndTime】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fundraisingStartTime】、【fundraisingEndTime】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fundraisingStartTime】、【fundraisingEndTime】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【fundraisingStartTime】、【fundraisingEndTime】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据 nodeCode=menu_xmthgl", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【手机】、【邮箱】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段 employeeNo", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据 key=NIKE_GUID, key=NIKE_INFO, key=NIKE_BUCKETNAME, key=NIKE_VAT_OTHER", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据 fromDB=cello、tableName=t_cello_orderinfo_nike", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "字段holderType  增加 20:志愿者活动详情 的注释", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "tips 调整   tips=2时会增加分割线", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据 code=nike,code=nikeProject,code=nikeVolActivity,code=nikeDonate,code=nikeVolTime,code=nikeStatistics1,code=volunteerActivityReview,code=nikeStatistics2,code=nikeStatistics3", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据 id = 6 的 NIKE定制 角色", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据 code=nike,code=nikeProject,code=nikeVolActivity,code=nikeDonate,code=nikeVolTime,code=nikeStatistics1,code=volunteerActivityReview,code=nikeStatistics2,code=nikeStatistics3", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据 [501]与[502]的关联 记录， roieLd = 6, menuId in 70~77", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据 scrm.crmuser与[501]的关联 记录， roieLd = 6, userId = scrm.crmuser.nike.id", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据 scrm.crmuser与[502]的关联 记录， roieLd = 6, userId = scrm.crmuser.nike.id, menuId in 70~77", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表（操作记录：记录活动、项目、月捐、日捐以及进展的提交、认领、审核、和平台侧的修改）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表（物资捐赠信息）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表（物资捐赠图片信息）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据【certificateMailReceiver】、【certificateMailTemp】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据，notificationsCode为“jgshyzzdqmb”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【progressType】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【endProgressFlag】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【endProgressFlag】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【endProgressFlag】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【endProgressFlag】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改code=remindProgress的数据，模板内容修改", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表（基础表）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表（基础表）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表（基础表）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表（基础表）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【evaluation_part1、evaluation_part2、evaluation_part3、evaluation_part4、good、anonymous】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【note、entityGuid】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据【QA_TEMPLATE】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加菜单数据【评价留言】、【爱心问答】，修改菜单排序", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加菜单数据code=scoreMessageReview、code=commentLoveAnswers", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【artificialStatus、artificialTime、institutionEntityGuid、type】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加基础数据【evaluation】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加菜单数据code为materialsDonate、MaterialsDonate", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【portraitFileId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【portraitFileId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【portraitFileId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【portraitFileId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【portraitFileId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【portraitFileId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【portraitFileId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【portraitFileId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【portraitFileId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【portraitFileId】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据id为81的机构信息展示", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据id为108，roleId=4（公募机构管理员） menuid=81(机构信息展示)", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加机构信息展示数据", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加菜单id为144，机构信息展示", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "字段type 增加 4:二维码图片 的注释", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "字段templateCharacter的长度扩充到500", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【validTime】【balanceTime】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加记录200【NIKE_QW_KEY】,201【NIKE_SECRETID】,202【NIKE_SECRETKEY】,203【NIKE_WECOM_URL】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "82-90", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "145-152", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "一期冗余的表，二期没有使用", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段【notice_time】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "SELECT \n\tNULL AS id,\nreplace(uuid(),\"-\",\"\") AS donate_guid,\n\tt1.username AS user_name,\n\tt1.createTime AS donate_time,\n\tt1.volTime AS hours,\n\tt1.projectGuid AS to_entity,\n\tt1.employeeNo AS emp_id,\n\tt1.mail AS mail,\n\tt1.`name` AS NAME,\n\tt1.phone AS phone,\n\tNULL AS message,\n\t1 AS `status`,\n\tt1.volReward AS vol_reward,\n\t1 AS apply_status \nFROM\n\tt_horn_volunteer_activity_join t1 \nWHERE\n\tt1.projectGuid IS NOT NULL \n\tAND `status` = 1\norder by t1.createTime asc", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "SELECT \n\tNULL AS id,\n\tt1.username AS user_name,\n\tNULL AS donate_guid,\n\tjoinGuid AS join_guid,\n\tt1.volTime AS hours \nFROM\n\tt_horn_volunteer_activity_join t1 \nWHERE\n\tt1.projectGuid IS NOT NULL \n\tAND `status` = 1\norder by t1.createTime asc", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【registerTime】、【registerNo】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加菜单数据code=numRemind", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "插入四行数据：\nnotificationsCode=fgmhdsh、notificationsCode=fgmxmsh、\nnotificationsCode=fgmyjsh、notificationsCode=fgmrjsh", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加code为zyxsdq的数据", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【publicReview】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【publicReview】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【publicReview】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【publicReview】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加key为【NIKE_MAX】的记录", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【testAmount】、【testCount】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "字段status 增加 3：产品已结束 的注释", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "更新字段“type”的值；删除字段“effectiveNum”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "更新字段“detailInfo”的的长度", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加菜单数据code=progress", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段字段【coverage】，修改【lastProgressTime】、【remindProgressTime】注释", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段字段【coverage】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段字段【coverage】，修改【lastProgressTime】、【remindProgressTime】注释", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段字段【coverage】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段字段【coverage】，修改【lastProgressTime】、【remindProgressTime】注释", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段字段【coverage】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改【lastProgressTime】、【remindProgressTime】注释", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "ype字段增加设置值“4：母计划项目；5：子计划项目”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表【公益项目子母计划关系表】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据id为155、156、157、158", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据id为91、92", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【isMatch（是否有配捐）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据“code：projectSubPublic”（子母项目）、和“code：projectSub”（子母项目）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【subProReviewIns：子计划审核机构（母计划的创建机构）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "字段【holderType】增加注释“11、子计划项目详情”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据“code：projectSub”（子母项目管理菜单）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据key为【yhProjctId】的记录", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【login_time：登录时间】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【avatarPicId：发起人\n头像图片】、【nickname：发起人昵称】、【targetStr：筹款标的】、【fundGoalsFlag：筹款标的区分  0：用户没有设置过（使用活动、项目、月捐）  1：用户有设置过；  默认为0】【fundGoalsAlias：捐款别名】、【target：筹款目标】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【toolsOperateFlg：月捐邀请审核标志（0：不审核；1：审核）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据【code=invitationReview（邀请捐审核菜单）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【editFlag：可编辑标识（0：不可编辑；1：可编辑）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "UPDATE `horn`.`t_horn_invitation` SET `editFlag` = 1 WHERE `createTime` < '替换发布时间';\nUPDATE `horn`.`t_horn_invitation` SET `editFlag` = 0 WHERE `createTime` >= '替换发布时间';", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【supportEn：是否支持英文版（0：不支持；1：支持）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据id为97的月捐看板", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据roleId=3（非公募机构管理员） menuid=97(月捐看板)\n添加数据roleId=4（公募机构管理员） menuid=97(月捐看板)", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加月捐看板数据", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "新建用户收藏的项目表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据【code=collectProject（收藏的项目）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据【code=scdxm（收藏的项目文件）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "新建大家都在关注表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据【code=follow（大家都在关注）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【showFlag=短链接管理flag（0：不显示；1：显示；）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据id为101的短链接管理", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据roleId=3（非公募机构管理员） menuid=101(短链接管理)\n添加数据roleId=4（公募机构管理员） menuid=101(短链接管理)", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加短链接管理数据", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【limitTime=有效时间】\n添加字段【status=状态（-1：删除，0：正常，1：已作废）】\n添加字段【type=类型（1：资源中心，2：联劝筹款产品）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "业务筹款产品短链接表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据【name=短链接管理】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "删除字段【userGuid=收藏者用户guid】\n添加字段【username=用户名】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【donatePcFlg  电脑端捐赠支持标志（0：不支持；1：支持）】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段【ipAddress】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加code为regUser、project、joint、donateRecord的数据", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "义卖奖券短信发送表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "用户密码错误记录", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据【mark=HARP_2019_USER_LOGIN_ERROR_CHECK、mark=HORN_USER_LOGIN_ERROR_CHECK、mark=ZITHER_USER_LOGIN_ERROR_CHECK】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据【mark=IMG_PUBLIC_KEY、mark=IMG_PRIVATE_KEY】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据【ckey=hidMsgPjtIds、ckey=hidAllDonPjtIds、ckey=hidDonMsgPjtIds】", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "1、修改字段注释：\nintervalDays：progressRemindType=0：反馈间隔天数；progressRemindType=1：结项逾期期限；\nday：天数（day>0：到期前几天；day<=0：到期后几天；）\nconten1：从未发布过进度反馈的提醒内容（day>0：即将逾期状态提醒内容；day<=0：已逾期状态提醒内容；）\nconten2：已发布过进度反馈的提醒内容（day>0：即将逾期状态提醒内容；day<=0：已逾期状态提醒内容；）\n\n2、扩充conten1、conten2、conten3长度到500（原255）\n\n3、添加字段：\nprogressRemindType：项目进展提醒类型（0：进展；1：结项；）\nstopTime：停用时间（为空则为使用中）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "1、修改字段注释：\nstatus：状态（-2：不提醒；-1：待提醒；0：未提交反馈；1：已提交反馈；2：无需反馈；99：已关闭；）\nholderId：所属公募机构Guid\nmailRemind：邮件提醒情况（-1：未设置；0：待提醒；1：已提醒；2：提醒失败；）\nnoticeRemind：站内信提醒情况（-1：未设置；0：待提醒；1：已提醒；2：提醒失败；）\npopupRemind：弹窗提醒情况（-1：未设置；0：待提醒；1：已提醒；2：提醒失败；）\n\n2、添加字段：\ncycleTime：周期开始时间\nday：天数（day>0：到期前几天；day<=0：到期后几天；）\nprogressRemindType：项目进展提醒类型（0：进展；1：结项；）\nprogressId：项目进展id\nprogressInsertTime：项目进展提交时间\nremark：不提醒/已关闭/无需反馈备注\n\n3、添加索引", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改字段注释：\nlastProgressTime：最新的反馈提交日期\nremindProgressTime：反馈提醒周期/结项逾期期限的开始时间", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改字段注释：\nlastProgressTime：最新的反馈提交日期\nremindProgressTime：反馈提醒周期/结项逾期期限的开始时间", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改字段注释：\nlastProgressTime：最新的反馈提交日期\nremindProgressTime：反馈提醒周期/结项逾期期限的开始时间", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "修改字段注释：\nlastProgressTime：最新的反馈提交日期\nremindProgressTime：反馈提醒周期/结项逾期期限的开始时间", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "新建表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "1、添加字段：\nruleType1：邮件提醒（0：需要；1：不需要）\nruleType2：站内信提醒（0：需要；1：不需要）\nruleType3：弹窗提醒（0：需要；1：不需要）\nstopTime：停用时间（为空则为使用中）\n\n2、扩充conten长度到500（原255）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "1、修改字段注释：\nproductId：筹款产品Guid\nstatus：状态（-2：不提醒；-1：待提醒；0：未更新；1：更新审核中；2：更新审核不通过；3：已更新；4：无需更新；99：已关闭；）\nholderId：所属公募机构Guid\nmailRemind：邮件提醒情况（-1：未设置；0：待提醒；1：已提醒；2：提醒失败；）\nnoticeRemind：站内信提醒情况（-1：未设置；0：待提醒；1：已提醒；2：提醒失败；）\npopupRemind：弹窗提醒情况（-1：未设置；0：待提醒；1：已提醒；2：提醒失败；）\n\n2、添加字段：\nproductIndex：筹款产品id\nday：天数（day>0：到期前几天；day<=0：到期后几天；）\nremindTime：提醒时间\nremark：不提醒/已关闭/无需更新备注\nnumUpdateTime：更新时间\n\n3、添加索引\n\n4、历史数据status的处理：\n1、t_horn_num_remind表，status字段值修改：\n-- 旧“3：产品已结束” 改为 新“99：已关闭”\nUPDATE t_horn_num_remind t1 SET t1.`status` = 99, t1.remark = \"筹款产品已结束，无需更新备案号。\" WHERE t1.`status` = 3;\n-- 旧“1：到期提醒中” 改为 新“0：未更新”\nUPDATE t_horn_num_remind t1 SET t1.`status` = 0 WHERE t1.`status` = 1;\n-- 旧“2：已更新” 改为 新“3：已更新”\nUPDATE t_horn_num_remind t1 SET t1.`status` = 3 WHERE t1.`status` = 2;", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "新建表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "新建表", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "id=\"172\"", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "1、修改注释：\nentityId：活动/项目/月捐/日捐\nentityGuid：活动/项目/月捐/日捐GUID\nremindTime：邮件、站内信提醒发送时间\n\n2、添加字段：\nremindId：提醒ID(rem开头）\ncontent：内容\n\n3、扩充noticeId长度到255（原32）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段：\nremindId：提醒ID(rem开头）\nsendType：通知类型（0：站内信；1：邮件）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "1、添加字段：\nremindId：提醒ID(rem开头）\n\n2、扩充sendGuid长度到255（原64）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "1、新建表\n2、添加索引", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "notificationsCode=“rjfkshmb”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加字段：portraitFlag\n\nstatus：进度状态（0：草稿；1：审核不通过；2：审核中；3：确认中（审核通过）；4：已确认（已发布）；5：确认不通过；6：审核过程中的删除；7：发布后的删除；）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据：ckey=“PROGRESS_REMIND_FLAG、NUM_REMIND_FLAG、CERTIFICATE_REMIND_FLAG”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "增加字段remark", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据：ckey=“apiUrlBoss3、isBoss3”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据：ckey=“aspInvoiceType、spInvoiceContract”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "添加数据：ckey=“invoiceSuspendStart、invoiceSuspendEnd、invoiceSuspendNotice、isInvoiceSuspend”", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}], "indexes": [], "constraints": [], "notes": []}, "更新履历(视图)": {"table_name": "更新履历(视图)", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "更新履历（存储过程）": {"table_name": "更新履历（存储过程）", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "登录日志表": {"table_name": "登录日志表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "操作日志表": {"table_name": "操作日志表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "错误日志表": {"table_name": "错误日志表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "基本配置表": {"table_name": "基本配置表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "定时器表": {"table_name": "定时器表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "平台升级公告表": {"table_name": "平台升级公告表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "标签密钥表": {"table_name": "标签密钥表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "首页轮播图管理表": {"table_name": "首页轮播图管理表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "horn图片表": {"table_name": "horn图片表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "短信模板": {"table_name": "短信模板", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "短信发送记录表": {"table_name": "短信发送记录表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "短信发送记录表（用户手机）": {"table_name": "短信发送记录表（用户手机）", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "邮箱验证表": {"table_name": "邮箱验证表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "邮件回复地址": {"table_name": "邮件回复地址", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "邮件验证信息表": {"table_name": "邮件验证信息表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}}}, "cas": {"system_key": "cas", "system_name": "CAS认证系统", "file_name": "cas_dataBaseModelDesign", "table_count": 17, "tables": {"更新履历": {"table_name": "更新履历", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "文档列表": {"table_name": "文档列表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "user_info": {"table_name": "user_info", "chinese_name": "", "description": "", "fields": [{"name": "个人永久捐赠账户", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "国籍（0：中国大陆；1：港澳台及外籍人士）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "个人筹款UID", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "职业信息（0：学生；1：公司职员；2：国企事业单位职员；3：自由职业者；4：公益从业人员；5：政府机关人员；6：媒体；7：其他）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "通讯地址（详细地址）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "邮政编码", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "紧急联系人姓名", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "紧急联系人手机", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "联系邮箱", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "微信用UNIONID", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "微信用昵称", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "省份 微信用", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "城市 微信用", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "国家 微信用", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "头像URL 微信用", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "注册IP", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "登录IP", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "登录时间", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "退出时间", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "原绑定微信（openid，认证登录用）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "原绑定QQ（openid，认证登录用）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "是否需要重置密码（0：不需要；1：需要）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "是否使用微信头像（0：不使用；1：使用）", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "微信（OPENID）公众号openid", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "用户是否订阅联劝公益公众号标识，值为0时，代表此用户没有关注该公众号 1：关注了", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}, {"name": "工作单位", "type": "", "length": "", "nullable": "", "default": "", "comment": "", "primary_key": false}], "indexes": [], "constraints": [], "notes": []}, "user_merge_info": {"table_name": "user_merge_info", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "invoice_info": {"table_name": "invoice_info", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "invoice_title_info": {"table_name": "invoice_title_info", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "com_config": {"table_name": "com_config", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "blacklist": {"table_name": "blacklist", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "validate_mail": {"table_name": "validate_mail", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "send_message": {"table_name": "send_message", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "pictures": {"table_name": "pictures", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "smsrecord": {"table_name": "smsrecord", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "smsstatistics": {"table_name": "smsstatistics", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "unique_key": {"table_name": "unique_key", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "access_log": {"table_name": "access_log", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "login_count": {"table_name": "login_count", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "user_info_synchro": {"table_name": "user_info_synchro", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}}}, "xxbz": {"system_key": "xxbz", "system_name": "小小包子系统", "file_name": "xxbz_dataBaseModelDesign", "table_count": 52, "tables": {"更新履历": {"table_name": "更新履历", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "视图更新履历": {"table_name": "视图更新履历", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "文档列表": {"table_name": "文档列表", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_activity": {"table_name": "t_xxbz_activity", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_user": {"table_name": "t_xxbz_user", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_briefMeeting": {"table_name": "t_xxbz_briefMeeting", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_activity_user": {"table_name": "t_xxbz_activity_user", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_user_family": {"table_name": "t_xxbz_user_family", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_family": {"table_name": "t_xxbz_family", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_family_team": {"table_name": "t_xxbz_family_team", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_invitation_tip": {"table_name": "t_xxbz_invitation_tip", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_family_match": {"table_name": "t_xxbz_family_match", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_team": {"table_name": "t_xxbz_team", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_teamtype": {"table_name": "t_xxbz_teamtype", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_invitation": {"table_name": "t_xxbz_invitation", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_termtype": {"table_name": "t_xxbz_termtype", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_activityterm": {"table_name": "t_xxbz_activityterm", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_activityterm_apply": {"table_name": "t_xxbz_activityterm_apply", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_enterprise": {"table_name": "t_xxbz_enterprise", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}, "t_xxbz_activity_enterprise": {"table_name": "t_xxbz_activity_enterprise", "chinese_name": "", "description": "", "fields": [], "indexes": [], "constraints": [], "notes": []}}}}